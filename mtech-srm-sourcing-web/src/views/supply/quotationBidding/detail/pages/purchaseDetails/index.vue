<!--采购明细-->
<template>
  <div class="full-height">
    <div class="tabs-wrap">
      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div class="bid-tips" v-if="bidTips" :style="{ left: tabSource.length * 158 + 'px' }">
        <mt-icon name="icon_card_info"></mt-icon>{{ $t(bidTips) }}
      </div>
    </div>

    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :edit-settings="editSettings"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
      @dataBound="dataBound"
      :class="['content', { hasAggregate: moduleType == 0 || moduleType == 2 }]"
    >
      <div
        slot="slot-filter-0"
        class="aggregate-container"
        v-if="moduleType == 0 || moduleType == 2"
      >
        <span>{{ $t('汇总') }}</span>
        <div class="amount" v-if="useTaxedPrice">
          <div>
            {{ $t('上次总金额（含税）：') }}<span>{{ lastTaxTotal }}</span>
          </div>
          <div>
            {{ $t('本次总金额（含税）：') }}<span>{{ currentTotal }}</span>
          </div>
        </div>
        <div class="amount" v-else>
          <div>
            {{ $t('上次总金额（未税）：') }}<span>{{ lastTotal }}</span>
          </div>
          <div>
            {{ $t('本次总金额（未税）：') }}<span>{{ currentTotal }}</span>
          </div>
        </div>
      </div>
    </mt-template-page>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import clickoutside from '@/directive/clickoutside'
import { v4 as uuidv4 } from 'uuid'
import { toolbar, toolbarNoImport, disabledToolbar, editColumnBefore } from './config'
import { utils } from '@mtech-common/utils'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import { Decimal32 } from '../../utils/utils'
import Decimal from 'decimal.js'
import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'
import cellDrawingView from 'COMPONENTS/NormalEdit/cellDrawingView' // 图纸协同查看
import { setChildWidth } from '@/views/common/columnData/utils'
import { isNullOrUndefined } from '@/utils/is'
const renameFieldAlias = {
  mt_supplier_rfx_header: 'rfx_header',
  mt_supplier_rfx_item: 'rfx_item',
  mt_supplier_rfx_item_ext: 'rfx_item_ext',
  mt_supplier_rfx_item_die: 'rfx_item_die',
  mt_supplier_bidding_item: 'bidding_item',
  mt_supplier_rfx_item_logistics: 'rfx_item_logistics',
  mt_supplier_bidding_item_logistics: 'bidding_item_logistics'
}

function getFieldAlias(field, tableName) {
  return field.replace(/.*?\./, renameFieldAlias[tableName] + '.')
}

const fieldDefinesMap = function (e) {
  if (!e.tableName || e.field.indexOf('.') === -1 || !renameFieldAlias[e.tableName]) {
    return e
  }
  return {
    ...e,
    searchOptions: {
      renameField: getFieldAlias(e.field, e.tableName)
    }
  }
}

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    fieldDefines: {
      type: Array,
      default: () => {
        return []
      }
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    //可编辑字段
    submitField: {
      type: Array,
      default: () => {
        return []
      }
    },
    parentRequired: {
      type: Array,
      default: () => {
        return []
      }
    },
    childRequired: {
      type: Array,
      default: () => {
        return []
      }
    },
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 币种编码
    currencyList: {
      type: Array,
      default: () => []
    },
    // 税率
    taxList: {
      type: Array,
      default: () => []
    },
    // 采购单位
    purUnitList: {
      type: Array,
      default: () => []
    },
    rfxCountdown: {
      type: Object,
      default: () => ({})
    },
    newPrice: {
      type: Number,
      default: 0
    },
    strategyConfig: {
      type: Array,
      default: () => []
    },
    ktFlag: {
      type: Number,
      default: () => {}
    }
  },
  watch: {
    $route(to, from) {
      if (from.name === 'offer-purchase-cost') {
        // 由成本分析页面返回，则刷新当前页面
        // this.$refs.templateRef.refreshCurrentGridData()
        this.backUrl()
      }
    },
    quotedPriceData() {
      this.watchQuotedPriceData()
    },
    fieldDefines: {
      immediate: true,
      handler() {
        this.watchQuotedPriceData()
      }
    },
    isShow: {
      immediate: true,
      handler() {
        this.initData()
      }
    },
    tabSource: {
      immediate: true,
      handler(n) {
        if (n.length > 0) {
          this.moduleType = n[0].moduleType
          if (
            this.pageConfig[0].grid.asyncConfig &&
            Object.keys(this.pageConfig[0].grid.asyncConfig).length
          ) {
            this.pageConfig[0].grid.asyncConfig.params.tabType = this.moduleType
          }
        }
      }
    },
    moduleType: {
      immediate: true,
      handler(v) {
        this.$set(this.downTemplateParams, 'tabType', v)
      }
    }
  },
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false,
          grid: {
            virtualPageSize: 30,
            customSelection: false,
            rowHeight: 40,
            enableVirtualization: true,
            pageSettings: {
              currentPage: 1,
              pageSize: 1000,
              pageSizes: [20, 50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            lineIndex: 1,
            allowFiltering: true,
            editSettings: {
              allowEditing: false
            },
            columnData: editColumnBefore,
            // frozenColumns: 1,
            asyncConfig: {
              url: this.$API.supplyQdetail.tenderItemsUrl,
              queryBuilderWrap: 'queryBuilderDTO',
              recordsPosition: 'data.records',
              params: {
                rfxId: this.$route.query.rfxId,
                tabType: 0
              },
              asyncSerializeList: this.serializeList
            },
            queryCellInfo: this.customiseCell,
            class: 'pe-edit-grid custom-toolbar-grid',
            recordDoubleClick: this.handleChildEndEdit
          }
        }
      ],
      editSettings: {
        allowEditing: true
      },
      //维护数组
      actionObj: [],
      //切换tabs页
      tabSource: [
        { title: this.$t('议价'), moduleType: 2 },
        { title: this.$t('当前报价'), moduleType: 0 },
        { title: this.$t('历史报价'), moduleType: 1 }
      ],
      moduleType: this.quotedPriceData.transferStatus != 32 ? 0 : 2,
      tenderItemsData: [],
      disabledTransferStatus: [38], //38, "技术投标中"   41, "商务投标中"
      sourceInfo: [],
      oldList: [], //判断是否保存旧数据
      requestUrls: {},
      downTemplateName: this.$t('采购明细模板'),
      downTemplateParams: {
        pageFlag: false,
        rfxId: this.$route.query.rfxId,
        tabType: 0
      },
      rowData: '',
      lastTotal: 0,
      lastTaxTotal: 0,
      currentTotal: 0,
      isRequote: false, // 是否重新报价
      isLoading: false,
      useTaxedPrice: false,
      txCompanyList: ['2M01', '2S06']
    }
  },
  computed: {
    // 是否可以报价
    canQuote() {
      return [31, 32, 41].includes(Number(this.quotedPriceData.transferStatus)) // 报价中 议价中 商务投标中
    },

    abateFlag() {
      return this.moduleType == 2 ? true : false
    },

    bidTips() {
      if (!Array.isArray(this.strategyConfig) || !this.strategyConfig.length) {
        return ''
      }
      let _priceControl = this.strategyConfig[0]['priceControl']
      console.log('当前策略配置', this.strategyConfig)
      const STR1 = this.$t('说明：本次寻源不限制报价行数，请勾选您需要提交报价的行')
      const STR2 = this.$t('说明：本次是整单报价，您需要对所有进行报价')
      const STR3 = this.$t('说明：本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行')
      const STR4 = this.$t('说明：请勾选您需要提交报价的行')
      let _tip = ''
      if (this.moduleType === 0) {
        //当前报价
        switch (_priceControl) {
          case 'unlimited':
            _tip = STR1
            break
          case 'all':
            _tip = STR2
            break
          case 'first_all':
            //firstPrice  1 首次 0 非首次
            if (this.quotedPriceData.firstPrice == 1) {
              _tip = STR2
            } else {
              _tip = STR3
            }
            break
          default:
            _tip = ''
            break
        }
      } else if (this.moduleType === 2) {
        //议价
        // switch (_priceControl) {
        //   case "unlimited":
        //     _tip = STR3;
        //     break;
        //   case "all":
        //     _tip = STR2;
        //     break;
        //   case "first_all":
        //     if (this.quotedPriceData.submitStatus === 0) {
        //       _tip = STR2;
        //     } else {
        //       _tip = STR3;
        //     }
        //     break;
        //   default:
        //     _tip = "";
        //     break;
        // }
        _tip = STR4
      } else {
        _tip = ''
      }
      return _tip
    },
    isFc() {
      return this.quotedPriceData.rfxGeneralType === 2
    },
    isGf() {
      return this.quotedPriceData.buType === 'GF'
    }
  },
  beforeDestroy() {
    this.$bus.$off('procurementRefreshPage')
    this.$bus.$off('syncOtherLine')
  },
  async mounted() {
    await this.getTxCompanyList()
    this.$bus.$on(`procurementRefreshPage`, () => {
      this.$refs.templateRef.refreshCurrentGridData()
    })
    this.$bus.$on('contentDialog', () => {
      if (!this.txCompanyList.includes(this.$route.query?.companyCode)) {
        this.$toast({
          content: this.$t('最小采购量需是最小包装数量的整数倍'),
          type: 'warning'
        })
      }
    })
    // 同步其他行
    this.$bus.$on('syncOtherLine', (data) => {
      this.syncOtherLine(data)
    })
    this.makeGridId()
    this.$bus.$on('allPriceChange', (data) => {
      this.pageConfig[0].grid.asyncConfig = {}
      // this.pageConfig[0].grid.dataSource.forEach((x, i) => {
      //   x.itemStages = data[i].itemStages;
      // });
      this.defineTaxedUnitPrice(data)
      this.pageConfig[0].grid.dataSource = data
      this.actionObj = data
      this.getCurrentTotal()
    })
    // this.getLastTotal();
    this.$bus.$on('specEndEdit', () => {
      this.endEdit()
      let _current = this.$refs.templateRef.getCurrentTabRef()
      let _selectGridRecords = _current.grid.getCurrentViewRecords()
      this.actionObj = _selectGridRecords
    })
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.dictionaryGetList(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    //单元格icons，点击
    handleClickCellTool() {},
    // 点击单元格内容
    async handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'costAnalysis') {
        let res = { code: 200 }
        if (this.isRequote) {
          res = await this.saveTableData()
          this.isRequote = false
          res.code === 200 && this.$emit('mainparticipateButton', 'getDataOnly')
        }
        if (res.code === 200) {
          this.$router.push({
            name: 'offer-purchase-cost',
            query: {
              rfxId: this.$route.query.rfxId,
              rfxItemId: data.rfxItemId,
              abateFlag: this.quotedPriceData.transferStatus === 32, // 还价标志：议价为true，否则为false
              refreshId: Date.now() // 每次进入页面进行数据更新
            }
          })
        }
      }
    },
    isRfqModding() {
      return this.$route?.query?.source === 'rfq'
    },
    makeGridId() {
      const tabUidMap = ['current', 'history', 'bargain']
      const key =
        this.$permission.gridId.supply[this.$route.query?.source]?.tabs?.items?.[
          tabUidMap[this.moduleType]
        ] + this.$route?.query?.rfxId
      const gridId = this.$md5(key)
      this.pageConfig[0].gridId = gridId
      return gridId
    },
    // 设置可编辑状态
    setAllowEditing(allowEditing) {
      if (allowEditing) {
        if (!this.canQuote) {
          return
        }
        if (+this.rfxCountdown.countDown <= Date.now()) {
          return
        }

        // 议价中 且 非议价Tab
        if (this.quotedPriceData.transferStatus === 32 && this.moduleType !== 2) {
          return
        }
      }
      this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', allowEditing)
    },
    initData() {
      if (this.quotedPriceData.transferStatus != 32) {
        //非议价状态，不显示议价tab
        this.tabSource = [
          { title: this.$t('当前报价'), moduleType: 0 },
          { title: this.$t('历史报价'), moduleType: 1 }
        ]
      } else {
        this.tabSource = [
          { title: this.$t('议价'), moduleType: 2 },
          { title: this.$t('当前报价'), moduleType: 0 },
          { title: this.$t('历史报价'), moduleType: 1 }
        ]
      }
      if (this.quotedPriceData.status == 1) {
        if (this.quotedPriceData.joinStatus == 0) {
          this.$set(this.pageConfig[0], 'toolbar', [])
          this.setAllowEditing(false)
          this.$set(this.pageConfig[0].grid, 'queryCellInfo', null)
          this.$set(this.pageConfig[0].grid, 'class', '')
        } else if (this.quotedPriceData.joinStatus == 1) {
          if (this.disabledTransferStatus.indexOf(this.quotedPriceData.transferStatus) > -1) {
            //商务投标中  技术投标中
            this.$set(this.pageConfig[0], 'toolbar', disabledToolbar)
          } else {
            this.defineGridToolbar()
          }
          this.setAllowEditing(true)
          this.$set(this.pageConfig[0].grid, 'queryCellInfo', this.customiseCell)
          this.$set(this.pageConfig[0].grid, 'class', 'pe-edit-grid custom-toolbar-grid')
        }
      }
    },
    defineGridToolbar() {
      if (Array.isArray(this.childFields) && this.childFields.length) {
        // 如果存在子层结构  隐藏导入操作
        this.$set(this.pageConfig[0], 'toolbar', toolbarNoImport)
      } else {
        this.$set(this.pageConfig[0], 'toolbar', this.isRfqModding() ? toolbar : toolbarNoImport)
      }
    },
    serializeGridList(list) {
      const { currentPage, pageSize } =
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.pageSettings
      list.forEach((row, index) => {
        row.addId = uuidv4()
        row.index = (currentPage - 1) * pageSize + index
        if (row.biddingItemDTO && typeof row.biddingItemDTO === 'object') {
          if (
            this.quotedPriceData.sourcingObjType === 'cost_factor' &&
            this.quotedPriceData.joinStatus !== 0
          ) {
            // 成本因子参与后设置默认值（币种编码、币种名称、供方税率编码、供方税率名称）
            row.biddingItemDTO.currencyCode = 'CNY'
            row.taxRateCode = 'J2'
          }
          // 获取采方税率 ?? 单据税率
          const taxRateCode = row.taxRateCode || this.quotedPriceData?.taxRateCode
          // 获取采方币种 ?? 单据币种
          const currencyCode =
            row?.biddingItemDTO?.currencyCode || this.quotedPriceData?.currencyCode

          // 设置销售员字段默认值
          if (!row.biddingItemDTO.salesPerson) {
            const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
            row.biddingItemDTO.salesPerson = userInfo.username || ''
          }

          if (
            taxRateCode &&
            !row.biddingItemDTO.bidTaxRateCode &&
            !row.biddingItemDTO.bidTaxRateValue
          ) {
            const taxRow = this.taxList.find((e) => e.taxItemCode === taxRateCode)
            if (taxRow) {
              row.biddingItemDTO.bidTaxRateCode = taxRow.taxItemCode
              row.biddingItemDTO.bidTaxRateName = taxRow.taxItemName
              row.biddingItemDTO.bidTaxRateValue = taxRow.taxRate
            }
          }

          if (row.biddingItemDTO.bidTaxRateCode && !row.biddingItemDTO.bidTaxRateValue) {
            const taxRow = this.taxList.find(
              (e) => e.taxItemCode === row.biddingItemDTO.bidTaxRateCode
            )
            if (taxRow) {
              row.biddingItemDTO.bidTaxRateCode = taxRow.taxItemCode
              row.biddingItemDTO.bidTaxRateName = taxRow.taxItemName
              row.biddingItemDTO.bidTaxRateValue = taxRow.taxRate
            }
          }

          if (
            currencyCode &&
            (!row.biddingItemDTO.bidCurrencyCode || !row.biddingItemDTO.bidCurrencyName)
          ) {
            const findRow = this.currencyList.find((e) => e.currencyCode === currencyCode)
            if (findRow) {
              row.biddingItemDTO.bidCurrencyCode = findRow.currencyCode
              row.biddingItemDTO.bidCurrencyName = findRow.currencyName
            }
          }

          if (!row.biddingItemDTO.bidPurUnitCode) {
            row.biddingItemDTO.bidPurUnitName = ''
          }

          // 获取采方采购单位
          if (
            row.purUnitCode &&
            (!row.biddingItemDTO.bidPurUnitCode || !row.biddingItemDTO.bidPurUnitName)
          ) {
            const findRow = this.purUnitList.find((e) => e.unitCode === row.purUnitCode)
            if (findRow) {
              row.biddingItemDTO.bidPurUnitCode = findRow.unitCode
              row.biddingItemDTO.bidPurUnitName = findRow.unitName
            }
          }

          // 转换率
          if (!row.biddingItemDTO.bidConversionRate && row.conversionRate) {
            row.biddingItemDTO.bidConversionRate = row.conversionRate
          }

          // 整机模组外发，单价未税=运费+加工费+加工费材料费
          if (this.quotedPriceData.sourcingObjType === 'module_out_going') {
            const { transportCost, processCost, processPartyMaterialCost, bidTaxRateValue } =
              row.biddingItemDTO

            // 单价（未税）
            row.biddingItemDTO.untaxedUnitPrice =
              transportCost + processCost + processPartyMaterialCost || 0

            // 单价（含税）
            let taxedUnitPrice = 0
            const rate = new Decimal32(bidTaxRateValue || 0).add(1).toString()
            if (bidTaxRateValue >= 0) {
              taxedUnitPrice = new Decimal32(row.biddingItemDTO.untaxedUnitPrice)
                .mul(rate)
                .toFixed(this.ktFlag == 1 || this.isFc || this.isGf ? 5 : PRICE_FRACTION_DIGITS)
              row.biddingItemDTO.taxedUnitPrice = taxedUnitPrice
            }
            row.biddingItemDTO.taxedUnitPrice = taxedUnitPrice
          }
          // 上次降幅%
          let _lastDeclinePercent = row.biddingItemDTO.lastDeclinePercent
          row.biddingItemDTO.lastDeclinePercent = !isNullOrUndefined(_lastDeclinePercent)
            ? _lastDeclinePercent > 0
              ? (Number(_lastDeclinePercent) * 100).toFixed(2)
              : 0
            : ''
        }
        row.stepNum = row.stepValue
        row.drawing = row.drawings ? JSON.stringify(row.drawings) : null //单独处理附件字段
        row.stepQuoteName =
          row.itemStageList && row.itemStageList.length > 0
            ? JSON.stringify(row.itemStageList)
            : null //单独处理阶梯报价
        row.supplierDrawing = row.supplierFileList ? JSON.stringify(row.supplierFileList) : null //单独处理"供应商附件"字段
        if (row.itemExtMap && row.itemExtMap.minQuoteRangeType == 1) {
          row.itemExtMap.minQuoteRange = new Decimal(row.itemExtMap.minQuoteRange)
            .mul(new Decimal(100))
            .toNumber()
        }
        // 添加成本分析操作列
        if (row.costModelId && row.costModelQuote == 1) {
          row.costAnalysis = this.$t('成本分析')
        }
        let _subItems = row['childItems']
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }
      })
    },

    //初始调用接口
    async serializeList(list) {
      this.$emit('update:newPrice', 0)
      this.serializeGridList(list)
      // this.setSharePriceUntaxed(list) // 前端初始不参与计算分摊后单价未税
      await this.initSourceInfo(list)
      this.actionObj = list
      this.oldList = utils.cloneDeep(list)
      return list
    },
    //设置上次分摊价格
    setSharePriceUntaxed(list) {
      // sharePriceUntaxed  分摊后单价（未税）
      // untaxedUnitPrice   单价（未税）
      // realSharePriceUntaxed  实际分摊价（未税
      //分摊价=父不含税单价+（子分摊为是实际分摊价）
      list.forEach((row) => {
        let _sharePriceUntaxed = 0
        _sharePriceUntaxed += +row.biddingItemDTO?.untaxedUnitPrice ?? 0
        let _subItems = row['childItems']
        if (Array.isArray(_subItems)) {
          _subItems.forEach((e) => {
            _sharePriceUntaxed += +e.biddingItemDTO?.realSharePriceUntaxed ?? 0
          })
        }
        row.biddingItemDTO.sharePriceUntaxed = _sharePriceUntaxed
      })
    },
    // 报价属性 价格生效方式
    sourceInfoConverter(sourceInfo) {
      for (const row of sourceInfo) {
        const offerAttributeMap = {
          1: 'mailing_price',
          2: 'standard_price'
        }
        const priceEffectiveModeMap = {
          5: 'in_warehouse',
          1: 'order_date'
        }
        priceEffectiveModeMap['NULL'] = 'out_warehouse'
        row.offerAttribute = offerAttributeMap[row.offerAttribute]
        row.priceEffectiveMode = priceEffectiveModeMap[row.priceEffectiveMode]
      }
      return sourceInfo
    },
    // 供应商信息, 关联报价属性、报价方式
    async initSourceInfo(dataSource) {
      if (!this.$route.query.rfxId || !this.quotedPriceData) {
        return
      }
      const categoryCodeList = dataSource.filter((e) => e.categoryCode).map((e) => e.categoryCode)
      const res = await this.$API.supplierTender
        .getSourceInfo({
          siteCode: this.quotedPriceData.siteCode,
          categoryCodeList,
          rfxId: this.$route.query.rfxId
        })
        .catch(() => {})
      if (!res || !Array.isArray(res.data)) {
        return
      }
      this.sourceInfo = this.sourceInfoConverter(res.data)
      // 合并报价方式、报价属性
      dataSource.forEach((ds) => {
        if (typeof ds.biddingItemDTO === 'object') {
          const sourceInfoRow = this.sourceInfo.find((e) => e.categoryCode === ds.categoryCode)
          //
          if (sourceInfoRow) {
            // quoteAttribute offerAttribute=报价属性 如果匹配到报价属性, 则不可编辑
            if (!ds.biddingItemDTO.quoteAttribute && sourceInfoRow.offerAttribute) {
              ds.biddingItemDTO.quoteAttribute = sourceInfoRow.offerAttribute
            }
            // quoteMode priceEffectiveMode=报价方式 如果匹配到报价方式，则不可编辑
            if (!ds.biddingItemDTO.quoteMode && sourceInfoRow.priceEffectiveMode) {
              ds.biddingItemDTO.quoteMode = sourceInfoRow.priceEffectiveMode
            }
          }
        }
      })
    },
    //监听参与状态改变 - TODO：优化
    async watchQuotedPriceData() {
      if (this.quotedPriceData.transferStatus != 32) {
        //非议价状态，不显示议价tab
        this.tabSource = [
          { title: this.$t('当前报价'), moduleType: 0 },
          { title: this.$t('历史报价'), moduleType: 1 }
        ]
      } else {
        this.tabSource = [
          { title: this.$t('议价'), moduleType: 2 },
          { title: this.$t('当前报价'), moduleType: 0 },
          { title: this.$t('历史报价'), moduleType: 1 }
        ]
      }
      // 设置sourcingObjType在 \src\views\supply\quotationBidding\detail\utils\girdInputHandelers 中使用
      sessionStorage.setItem('purDetailKtFlag', this.quotedPriceData.sourcingObjType)
      sessionStorage.setItem('companyCode', this.quotedPriceData.companyCode)
      sessionStorage.setItem('rfxGeneralType', this.quotedPriceData.rfxGeneralType)
      sessionStorage.setItem('buType', this.quotedPriceData.buType)
      await this.getLastTotal()
      this.initGridConfig()
      if (this.quotedPriceData.status == 1) {
        if (this.quotedPriceData.joinStatus == 0) {
          this.$set(this.pageConfig[0], 'toolbar', [])
          this.setAllowEditing(false)
          this.$set(this.pageConfig[0].grid, 'queryCellInfo', null)
          this.$set(this.pageConfig[0].grid, 'class', '')
        } else if (this.quotedPriceData.joinStatus == 1) {
          if (this.disabledTransferStatus.indexOf(this.quotedPriceData.transferStatus) > -1) {
            //商务投标中  技术投标中
            this.$set(this.pageConfig[0], 'toolbar', disabledToolbar)
          } else {
            this.defineGridToolbar()
          }
          this.setAllowEditing(true)
          this.$set(this.pageConfig[0].grid, 'queryCellInfo', this.customiseCell)
          this.$set(this.pageConfig[0].grid, 'class', 'pe-edit-grid custom-toolbar-grid')
        }
      }
      this.childIsEdit = this.pageConfig[0].grid?.editSettings?.allowEditing
    },
    // 点击跳转 TODO:优化
    async handleSelectTab(idx) {
      this.moduleType = this.tabSource[idx].moduleType
      this.isRequote = false
      this.makeGridId()
      this.pageConfig[0].grid.asyncConfig.params = {
        ...this.pageConfig[0].grid.asyncConfig.params,
        tabType: this.moduleType
      }
      await this.getLastTotal()
      this.initGridConfig()
      let columnData = [...this.pageConfig[0].grid.columnData].map(fieldDefinesMap)
      const PRICE_STATUS = {
        0: this.$t('未提交'),
        1: this.$t('已报价'),
        2: this.$t('已接收'),
        3: this.$t('已拒绝'),
        4: this.$t('已议价'),
        5: this.$t('已中标'),
        6: this.$t('待定点'),
        7: this.$t('议价中')
      }
      let _preColumns = [
        {
          field: 'lineNo',
          width: '80',
          headerText: this.$t('行号'),
          allowEditing: false,
          allowFiltering: false
        },
        {
          field: 'biddingItemDTO.priceStatus',
          headerText: this.$t('状态'),
          allowEditing: false,
          // eslint-disable-next-line no-unused-vars
          formatter: function ({ field }, item) {
            if (
              item?.biddingItemDTO?.priceStatus === null ||
              item?.biddingItemDTO?.priceStatus === '' ||
              item?.biddingItemDTO?.priceStatus === undefined
            ) {
              return this.$t('未报价')
            } else {
              let _cellValue = item?.biddingItemDTO?.priceStatus ?? 0
              return PRICE_STATUS[_cellValue] ?? this.$t('未报价')
            }
          }
        }
      ]
      let _fields = columnData.map((e) => e.field)
      let _types = columnData.map((x) => x.type)

      if (!_fields.includes('lineNo') && !_fields.includes('biddingItemDTO.priceStatus')) {
        columnData = _preColumns.concat(columnData)
      }
      if (this.moduleType === 2 && !_fields.includes('biddingItemDTO.abateReason')) {
        columnData.splice(2, 0, {
          field: 'biddingItemDTO.abateReason',
          headerText: this.$t('议价理由'),
          allowEditing: false
        })
      }
      if (this.isSelectPriceControl() && !_types.includes('checkbox')) {
        this.$set(this.pageConfig[0].grid, 'customSelection', true)
      }
      this.$set(this.pageConfig[0]['grid'], 'columnData', columnData)
      if (this.quotedPriceData.status == 1) {
        if (this.quotedPriceData.joinStatus == 0) {
          this.$set(this.pageConfig[0], 'toolbar', [])
          this.setAllowEditing(false)
          this.$set(this.pageConfig[0].grid, 'class', '')
        } else if (this.quotedPriceData.joinStatus == 1) {
          if (this.moduleType == 0 || this.moduleType == 2) {
            if (this.disabledTransferStatus.indexOf(this.quotedPriceData.transferStatus) > -1) {
              //商务投标中  技术投标中
              this.$set(this.pageConfig[0], 'toolbar', disabledToolbar)
            } else {
              this.defineGridToolbar()
              if (this.quotedPriceData.transferStatus === 32 && this.moduleType !== 2) {
                this.setAllowEditing(false)
              } else {
                this.setAllowEditing(true)
              }
            }
            this.$set(this.pageConfig[0].grid, 'queryCellInfo', this.customiseCell)
            this.$set(this.pageConfig[0].grid, 'class', 'pe-edit-grid custom-toolbar-grid')
          } else if (this.moduleType === 1) {
            this.$set(this.pageConfig[0], 'toolbar', [])
            this.setAllowEditing(false)
            this.$set(this.pageConfig[0].grid, 'queryCellInfo', null)
            this.$set(this.pageConfig[0].grid, 'class', '')
          }
        }
      }
    },
    //点击头部
    handleClickToolBar({ toolbar }) {
      switch (toolbar.id) {
        case 'save':
          this.endEdit()
          this.handleClickSaves()
          break
        case 'quote': // 重新报价
          this.endEdit()
          this.handleClickQuote()
          break
        case 'upload': // 导入
          this.handleUpload()
          break
        default:
          break
      }
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing && args.column.field !== 'customChecked') {
        args.cell.classList.add('bg-grey')
      } else {
        args.cell.classList.add('bg-orange')
      }
    },
    dataBound() {
      if (this.moduleType == 0) {
        if (Array.isArray(this.childFields) && this.childFields.length) {
          //数据加载后，如果是父子结构，展开
          let _current = this.$refs.templateRef.getCurrentTabRef()
          _current?.grid.detailExpandAll()
        }
      }
    },
    //行内进入事件
    actionBegin(e) {
      if (this.quotedPriceData.companyCode == '1503' && e.row && e.row.editInstance) {
        e.row.editInstance.setOptions('biddingItemDTO.quoteEffectiveEndDate', {
          disabled: true
        })
      }
    },
    //行内离开事件
    actionComplete(e, tag = 'parent') {
      if (this.quotedPriceData.companyCode == '1503' && e.row && e.row.editInstance) {
        e.row.editInstance.setOptions('biddingItemDTO.quoteEffectiveEndDate', {
          disabled: true
        })
      }
      if (e.requestType === 'beginEdit') {
        //白电场景，==0时，禁止编辑‘有效期至’
        if (e.rowData.quoteEffectiveEndDateEditAble === 0 || this.isGf) {
          e.row.editInstance.setOptions('biddingItemDTO.quoteEffectiveEndDate', {
            disabled: true
          })
        }
        //光伏场景，禁止编辑 有效期从，有效期至
        if (this.isGf) {
          e.row.editInstance.setOptions('biddingItemDTO.quoteEffectiveStartDate', {
            disabled: true
          })
        }
        if (
          e.rowData?.costModelQuote === 1 ||
          (tag === 'subItems' &&
            this.quotedPriceData.sourcingObjType === 'combination' &&
            e.rowData?.selfPurchasing === 1)
        ) {
          e.row.editInstance.setOptions('biddingItemDTO.untaxedUnitPrice', {
            disabled: true,
            readonly: true
          })
        }
        // 如果币种为越南盾或者日元，则未税单价和含税单价编辑不允许输入小时
        if (['JPY', 'VND'].includes(e.rowData?.biddingItemDTO?.bidCurrencyCode)) {
          e.row.editInstance.setOptions('biddingItemDTO.untaxedUnitPrice', {
            precision: '0'
          })
          e.row.editInstance.setOptions('biddingItemDTO.taxedUnitPrice', {
            precision: '0'
          })
        }
      }
      if (e.requestType == 'save') {
        if (this.actionObj.length == 0) {
          this.actionObj.push(e.data)
        } else {
          if (tag === 'parent') {
            let hasSave = false
            let _list = this.actionObj
            for (let i = 0; i < _list.length; i++) {
              if (_list[i].rfxItemId == e.data.rfxItemId) {
                _list[i] = e.data
                hasSave = true
                break
              }
            }
            if (!hasSave) {
              _list.push(e.data)
            }
            this.setSharePriceUntaxed(_list)
            this.actionObj = _list
          } else {
            let _list = this.actionObj
            for (let i = 0; i < _list.length; i++) {
              let _subItems = _list[i]['childItems']
              if (Array.isArray(_subItems)) {
                _subItems.forEach((s, i) => {
                  if (s.rfxItemId == e.data.rfxItemId) {
                    _subItems[i] = e.data
                  }
                })
              }
            }
            this.setSharePriceUntaxed(_list)
            this.actionObj = _list
          }
        }
        this.getCurrentTotal()
      }
      // combination.actionCompleteAfter.call(this, e, tag)
    },
    serializeSaveParams(list) {
      let res = []
      list.forEach((e) => {
        let _sub = e?.childItems
        delete e.childItems
        res.push(e)
        if (Array.isArray(_sub)) {
          res = res.concat(_sub)
        }
      })
      return res
    },
    checkFields(obj, fields) {
      let res = true,
        msg = ''
      for (let i = 0; i < fields.length; i++) {
        let field = fields[i]['fieldCode']
        if (!obj['biddingItemDTO'][field] && obj['biddingItemDTO'][field] !== 0) {
          res = false
          let _field = this.fieldDefines.find((e) => e.field === `biddingItemDTO.${field}`)
          if (_field?.headerText) {
            msg = `字段：${_field.headerText}，为必填项`
          }
          break
        }
      }
      return { res, msg }
    },
    checkRequiredFields(list) {
      let _cloneObj = utils.cloneDeep(list)
      let _parentRequired = utils.cloneDeep(this.parentRequired)
      let _childRequired = utils.cloneDeep(this.childRequired)
      let checked = true,
        checkedMsg = ''
      let _priceControl = this.strategyConfig[0]['priceControl']
      let _current = this.$refs.templateRef.getCurrentTabRef()
      let _selectGridRecords = []
      _current.gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          _selectGridRecords.push(item)
        }
      })
      if (_priceControl === 'unlimited') {
        if (Array.isArray(_selectGridRecords)) {
          for (let i = 0; i < _selectGridRecords.length; i++) {
            let p = _selectGridRecords[i]
            let { res, msg } = this.checkFields(p, _parentRequired)
            if (!res) {
              checked = false
              checkedMsg = msg
              break
            }
            let _Check = true
            if (Array.isArray(p.childItems) && p.childItems.length) {
              for (let j = 0; j < p.childItems.length; j++) {
                let c = p.childItems[j]
                let { res, msg } = this.checkFields(c, _childRequired)
                if (!res) {
                  _Check = false
                  checkedMsg = `第${i + 1}父行，第${j + 1}子行，${msg}`
                  break
                }
              }
            }
            if (!_Check) {
              checked = false
              break
            }
          }
        }
        return { checked, checkedMsg }
      } else if (Array.isArray(_cloneObj) && _cloneObj.length) {
        for (let i = 0; i < _cloneObj.length; i++) {
          let p = _cloneObj[i]
          let { res, msg } = this.checkFields(p, _parentRequired)
          if (!res) {
            checked = false
            checkedMsg = `第${i + 1}行，${msg}`
            break
          }
          let _Check = true
          if (Array.isArray(p.childItems) && p.childItems.length) {
            for (let j = 0; j < p.childItems.length; j++) {
              let c = p.childItems[j]
              let { res, msg } = this.checkFields(c, _childRequired)
              if (!res) {
                _Check = false
                checkedMsg = `第${i + 1}父行，第${j + 1}子行，${msg}`
                break
              }
            }
          }
          if (!_Check) {
            checked = false
            break
          }
        }
      }
      return { checked, checkedMsg }
    },
    // 保存 TODO:优化
    handleClickSaves() {
      this.endEdit()
      this.getActionObj()

      // let { checked, checkedMsg } = this.checkRequiredFields(this.actionObj)
      // if (!checked) {
      //   this.$toast({
      //     content: checkedMsg,
      //     type: 'error'
      //   })
      //   return
      // }
      let _cloneObj = this.serializeSaveParams(utils.cloneDeep(this.actionObj))

      let _priceControl = this.strategyConfig[0]['priceControl']
      if (_priceControl) {
        //如果设置了报价规则，才执行。未设置报价规则，跳过
        let _current = this.$refs.templateRef.getCurrentTabRef()
        let _selectGridRecords = []
        _current.gridRef.dataSource.forEach((item) => {
          if (item.customChecked) {
            _selectGridRecords.push(item)
          }
        })
        if (this.moduleType === 2) {
          // 议价，保存不用勾选
          _selectGridRecords = _current.grid.getCurrentViewRecords()
          _cloneObj = this.serializeSaveParams(utils.cloneDeep(_selectGridRecords))
        } else if (this.moduleType === 0) {
          //无限制   首次整单报价(非首次，使用勾选数据)
          if (_priceControl === 'first_all' && this.quotedPriceData.firstPrice == 0) {
            //firstPrice 1 首次 0 非首次
            if (_selectGridRecords.length < 1) {
              this.$toast({
                content: this.$t('本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行'),
                type: 'warning'
              })
              return
            } else {
              _cloneObj = this.serializeSaveParams(utils.cloneDeep(_selectGridRecords))
            }
          } else {
            _cloneObj = this.serializeSaveParams(utils.cloneDeep(this.actionObj))
          }
        }
      } else {
        // 如果未配置过‘报价规则’，报价限制走无限制
        let _current = this.$refs.templateRef.getCurrentTabRef()
        let _selectGridRecords = _current.grid.getCurrentViewRecords()
        if (_selectGridRecords.length < 1) {
          this.$toast({
            content: this.$t('请勾选您需要提交报价的行'),
            type: 'warning'
          })
          return
        } else {
          _cloneObj = this.serializeSaveParams(utils.cloneDeep(_selectGridRecords))
        }
      }
      // 数据校验 - 最小采购量&最小包装量
      let flag = this.validSaveParams(_cloneObj)
      if (!flag) return
      //采购单位和基本单位不一致的弹框
      let visible = false
      const hasPurUnitCode = this.pageConfig[0].grid.columnData.find(({ field }) => {
        return ['biddingItemDTO.bidPurUnitCode', 'purUnitCode'].includes(field)
      })
      const hasUnitCode = this.pageConfig[0].grid.columnData.find(({ field }) => {
        return ['unitCode'].includes(field)
      })
      // 父子结构不校验不一致
      if (hasPurUnitCode && hasUnitCode && this.quotedPriceData.sourcingObjType !== 'HIERARCHY') {
        visible = _cloneObj.find((row) => {
          const purUnitCode = row.biddingItemDTO.bidPurUnitCode || row.purUnitCode
          const unitCode = row.unitCode
          return purUnitCode !== unitCode
        })
      }

      if (_cloneObj.length <= 0) {
        this.$toast({
          content: this.$t('没有编辑数据不能保存'),
          type: 'warning'
        })
        return
      }

      // 抽离处理数据逻辑
      let param1 = this.mergeSaveParams(_cloneObj)

      // 物流参数处理
      if (Array.isArray(param1.bidPriceItems)) {
        param1.bidPriceItems = param1.bidPriceItems.map((bidPriceItem, index) => {
          return {
            ...bidPriceItem,
            biddingItemLogisticsDTO: _cloneObj?.[index]?.biddingItemLogisticsDTO
          }
        })
      }
      if (visible) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('采购单位和基本单位不一致，请确认转换率是否正确')
          },
          success: () => {
            this.$API.supplyQdetail.priceSave(param1).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$emit('mainparticipateButton')
                // this.actionObj = [] // 清空，空id会导致历史报价重复
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            this.backUrl()
          }
        })
      } else {
        this.$store.commit('startLoading')
        this.$API.supplyQdetail.priceSave(param1).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$store.commit('endLoading')
            if (this.$route.query?.source === 'invite_bids') {
              this.$emit('mainparticipateButton', 'getDataOnly')
            } else {
              this.$emit('mainparticipateButton')
            }
            // this.actionObj = [] // 清空，空id会导致历史报价重复
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        this.backUrl()
      }
    },
    // 保存页面数据 (作跳转保存用)
    async saveTableData() {
      this.endEdit()
      let _cloneObj = this.serializeSaveParams(utils.cloneDeep(this.actionObj))
      // 数据校验 - 最小采购量&最小包装量
      let flag = this.validSaveParams(_cloneObj)
      if (!flag) return
      let params = this.mergeSaveParams(_cloneObj)
      return await this.$API.supplyQdetail.priceSave(params)
    },
    // 数据校验 - 最小采购量&最小包装量
    validSaveParams(_cloneObj) {
      let switchButton = true
      _cloneObj.map((item, index) => {
        if (
          item.biddingItemDTO.minPurQuantity != undefined &&
          item.biddingItemDTO.minPurQuantity != null &&
          item.biddingItemDTO.minPurQuantity != '' &&
          item.biddingItemDTO.minPackageQuantity != undefined &&
          item.biddingItemDTO.minPackageQuantity != null &&
          item.biddingItemDTO.minPackageQuantity != ''
        ) {
          let adviseMinPurQuantity = item.biddingItemDTO.minPurQuantity
          let adviseMinPackageQuantity = item.biddingItemDTO.minPackageQuantity
          let number = adviseMinPurQuantity / adviseMinPackageQuantity
          if (
            Math.floor(number) !== number &&
            !this.txCompanyList.includes(this.$route.query?.companyCode)
          ) {
            this.$toast({
              content: `第${index + 1}行` + this.$t('最小采购量需是最小包装数量的整数倍'),
              type: 'warning'
            })
            switchButton = false
            return
          }
        }
      })
      return switchButton
    },
    // 合并参数 TODO:优化
    mergeSaveParams(_cloneObj) {
      let arr = []
      let params = []
      let rfxId = this.quotedPriceData.rfxId
      let tenantId = this.quotedPriceData.tenantId
      //统一arr数组length长度
      for (let i = 0; i < _cloneObj.length; i++) {
        arr.push({})
      }
      //arr对象内添加code
      for (let i = 0; i < arr.length; i++) {
        if (typeof _cloneObj[i]?.biddingItemDTO?.bidTaxRateValue !== 'undefined') {
          arr[i].bidTaxRateValue = _cloneObj[i].biddingItemDTO.bidTaxRateValue
          if (
            _cloneObj[i].biddingItemDTO.bidTaxRateCode &&
            !_cloneObj[i].biddingItemDTO.bidTaxRateValue
          ) {
            const taxRow = this.taxList.find(
              (e) => e.taxItemCode === _cloneObj[i].biddingItemDTO.bidTaxRateCode
            )
            arr[i].bidTaxRateValue = taxRow?.taxRate
          }
        }

        for (let j = 0; j < this.submitField.length; j++) {
          let FieldCode = this.submitField[j].fieldCode
          if (FieldCode === 'supplierDrawing') {
            let _files = _cloneObj[i][FieldCode]
            if (_files && typeof _files == 'string') {
              _files = JSON.parse(_files)
              if (_files.length) {
                _files.forEach((f) => {
                  if (!f?.sysFileId) {
                    f.sysFileId = f.id
                    delete f.id
                  }
                })
              }
            } else {
              _files = []
            }
            arr[i]['supplierFileList'] = _files
          } else {
            arr[i][FieldCode] = _cloneObj[i][FieldCode]
            arr[i][FieldCode] = _cloneObj[i]['biddingItemDTO'][FieldCode]
          }
        }
      }

      for (let i = 0; i < arr.length; i++) {
        arr[i].biddingId = _cloneObj[i]['biddingItemDTO'].biddingId
          ? _cloneObj[i]['biddingItemDTO'].biddingId
          : null
        arr[i].biddingItemId = _cloneObj[i]['biddingItemDTO'].biddingItemId
          ? _cloneObj[i]['biddingItemDTO'].biddingItemId
          : null
        arr[i].rfxItemId = _cloneObj[i].rfxItemId ? _cloneObj[i].rfxItemId : null
        arr[i].itemStages = _cloneObj[i]['itemStages']
        arr[i].priceUnitName = _cloneObj[i]['priceUnitName']
      }
      if (this.moduleType == 0) {
        params = {
          abateFlag: false,
          bidPriceItems: arr,
          rfxId: rfxId,
          submitStatus: 0,
          tenantId: tenantId
        }
      }
      if (this.moduleType == 2) {
        params = {
          abateFlag: true,
          bidPriceItems: arr,
          rfxId: rfxId,
          submitStatus: 0,
          tenantId: tenantId,
          newPrice: this.newPrice
        }
      }
      return params
    },
    backUrl() {
      this.pageConfig[0].asyncConfig = {
        url: this.$API.supplyQdetail.tenderItemsUrl,
        queryBuilderWrap: 'queryBuilderDTO',
        recordsPosition: 'data.records',
        params: {
          rfxId: this.$route.query.rfxId,
          tabType: 0
        },
        asyncSerializeList: this.serializeList
      }
    },
    // 上层通过ref的调用
    async getActionObj() {
      return this.actionObj
    },
    resetQuoteParams(list) {
      if (Array.isArray(list)) {
        list.forEach((e) => {
          e['biddingItemDTO'].biddingId = 0
          e['biddingItemDTO'].biddingItemId = 0
          e['biddingItemDTO'].submitStatus = 0
          if (Array.isArray(e?.childItems)) {
            this.resetQuoteParams(e.childItems)
          }
        })
      }
    },
    // 重新报价
    handleClickQuote() {
      this.$emit('update:newPrice', 1)

      this.fieldDefines.forEach((item) => {
        // //可以编辑
        const allowEditingTbs = ['mt_supplier_bidding_item', 'mt_supplier_bidding_item_logistics']
        if (allowEditingTbs.includes(item.tableName)) {
          item.allowEditing = true
        } else {
          item.allowEditing = false
        }
      })

      if (this.moduleType == 0) {
        this.resetQuoteParams(this.actionObj)
        console.log('重新报价', this.actionObj)
      }

      this.$toast({
        content: this.$t('请调整物料价格，提交报价！'),
        type: 'warning'
      })
      this.isRequote = true
    },
    startEdit(index) {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      this.$bus.$emit(`subGridEndEdit`)
    },
    handleParentEndEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
    },
    handleChildEndEdit(e) {
      this.$bus.$emit(`subGridEndEdit`)
      this.rowData = e.rowData
    },
    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'rfxRequireDetail',
        templateUrl: 'supExportPriceTemplete',
        uploadUrl: `supImportPriceTemplete`,
        rfxId: this.$route.query.rfxId
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    },
    defineGridColumns(fieldDefines) {
      let _columnData = [...fieldDefines].map(fieldDefinesMap)
      const PRICE_STATUS = {
        0: this.$t('未提交'),
        1: this.$t('已报价'),
        2: this.$t('已接收'),
        3: this.$t('已拒绝'),
        4: this.$t('已议价'),
        5: this.$t('已中标'),
        6: this.$t('待定点'),
        7: this.$t('议价中')
      }
      let _preColumns = [
        {
          field: 'lineNo',
          width: '80',
          headerText: this.$t('行号'),
          allowEditing: false,
          allowFiltering: false
        },
        {
          field: 'biddingItemDTO.priceStatus',
          headerText: this.$t('状态'),
          allowEditing: false,
          // eslint-disable-next-line no-empty-pattern
          formatter: function ({}, item) {
            if (
              item?.biddingItemDTO?.priceStatus === null ||
              item?.biddingItemDTO?.priceStatus === '' ||
              item?.biddingItemDTO?.priceStatus === undefined
            ) {
              return '未报价'
            } else {
              let _cellValue = item?.biddingItemDTO?.priceStatus ?? 0
              return PRICE_STATUS[_cellValue] ?? this.$t('未报价')
            }
          }
        }
      ]
      _columnData = _preColumns.concat(_columnData)
      if (this.moduleType === 2) {
        _columnData.splice(2, 0, {
          field: 'biddingItemDTO.abateReason',
          headerText: this.$t('议价理由'),
          allowEditing: false
        })
      }
      if (this.isSelectPriceControl()) {
        this.$set(this.pageConfig[0].grid, 'customSelection', true)
      }
      return _columnData
    },
    // 初始化列
    initGridConfig() {
      if (!Array.isArray(this.fieldDefines) || this.fieldDefines?.length <= 0) return // 父级列字段
      const editInstance = createEditInstance()
      let _columnData = this.defineGridColumns(utils.cloneDeep(this.fieldDefines))
      let that = this
      const priceUnitField = ['priceUnitName', 'biddingItemDTO.priceUnitName']
      let _fixField = [
        // 合格供应商禁用编辑字段
        'biddingItemDTO.bidCurrencyCode',
        'biddingItemDTO.bidCurrencyName',
        'biddingItemDTO.quoteAttribute',
        'biddingItemDTO.quoteMode',
        'biddingItemDTO.bidTaxRateCode',
        'biddingItemDTO.bidTaxRateName',
        'biddingItemDTO.bidTaxRateValue'
      ]
      _columnData.forEach((item) => {
        if (['costModelName', 'costModelVersionCode', 'costModelCode'].includes(item.field)) {
          item.width = 0
        } else if (item.field == 'costModelId') {
          item.width = 0
        } else if (item.field == 'costAnalysis') {
          item.cssClass = 'field-content'
        } else if (item.field == 'costModelQuote') {
          item.valueConverter = {
            type: 'map',
            map: { 0: this.$t('否'), 1: this.$t('是') }
          }
          // item.valueAccessor = (field, data) => {
          //   return data[field] == 1 ? this.$t("是") : this.$t("否");
          // };
          item.edit = editInstance.create({
            getEditConfig: () => ({
              type: 'select',
              'show-clear-button': true,
              fields: { value: 'value', text: 'text' },
              dataSource: [
                { text: this.$t('是'), value: 1 },
                { text: this.$t('否'), value: 0 }
              ],
              readonly: true,
              disabled: true
            })
          })
        } else if (item.field == 'biddingItemDTO.untaxedUnitPrice') {
          // 如果是“单独模具询报价”模式则要通过输入含税单价计算未税单价
          // 整机模组外发，单价未税自动计算（运费+加工费+加工费材料费）
          if (
            this.ktFlag == 1 ||
            this.quotedPriceData.sourcingObjType === 'single_module' ||
            this.quotedPriceData.sourcingObjType === 'module_out_going'
          ) {
            item.allowEditing = false
            item.editConfig.readonly = true
            item.editConfig.precision = '5'
          }
          if (this.isFc || this.isGf) {
            item.editConfig.precision = '5'
          }
        } else if (item.field == 'biddingItemDTO.taxedUnitPrice') {
          if (this.ktFlag == 1 || this.quotedPriceData.sourcingObjType === 'single_module') {
            item.editConfig.precision = '5'
          } else {
            item.allowEditing = false
            item.editConfig.readonly = true
          }
        } else if (
          item.field == 'biddingItemDTO.bidHistoryPrice' ||
          item.field == 'biddingItemDTO.lastDeclinePercent'
        ) {
          // 历史报价、上次报价降幅不可编辑
          item.allowEditing = false
        } else if (priceUnitField.includes(item.field)) {
          item.allowEditing = false
          if (this.quotedPriceData.rfxGeneralType === 2) {
            // item.edit 跟 item.editTemplate不兼容
            if (item.edit) delete item.edit
            const template = {
              template: `<span>{{text}}</span>`,
              data() {
                return {
                  data: {}
                }
              },
              computed: {
                text() {
                  return this.data.priceUnitName === '1' ? this.$t('元') : this.$t('万元')
                }
              }
            }
            item.editTemplate = function () {
              return { template }
            }
          }
          item.valueAccessor = (field, data) => {
            // 分通采和非采两种类型
            if (this.quotedPriceData.rfxGeneralType === 2) {
              // 非采
              const purUnitNameDataSource = [
                { text: this.$t('元'), value: '1' },
                { text: this.$t('万元'), value: '0.0001' }
              ]
              const item = purUnitNameDataSource.find((el) => el.value === data.priceUnitName)
              return item ? item.text : ''
            }
            return data.priceUnitName || ''
          }
        } else if (item.field == 'stepQuote') {
          // 是否阶梯报价
          item.formatter = function ({ field }, item) {
            const cellVal = item[field]
            return cellVal === 1 ? that.$t('是') : that.$t('否')
          }
          item.allowEditing = true
          item.edit = editInstance.create({
            getEditConfig: () => ({
              type: 'select',
              'show-clear-button': true,
              fields: { value: 'value', text: 'text' },
              dataSource: [
                { text: this.$t('是'), value: 1 },
                { text: this.$t('否'), value: 0 }
              ],
              readonly: true,
              disabled: true
            })
          })
          item.queryType = 'select'
          item.valueConverter = {
            type: 'map',
            map: {
              0: this.$t('否'),
              1: this.$t('是')
            }
          }
        } else if (item.field == 'stepQuoteType') {
          // 阶梯类型
          item.formatter = function ({ field }, item) {
            const cellVal = item[field]
            return cellVal === 0
              ? this.$t('数量累计阶梯')
              : cellVal === 3
              ? this.$t('数量逐层阶梯')
              : ''
          }
          item.allowEditing = true
          item.edit = editInstance.create({
            getEditConfig: () => ({
              type: 'select',
              'show-clear-button': true,
              fields: { value: 'value', text: 'text' },
              dataSource: [
                { value: 0, text: this.$t('数量累计阶梯') },
                { value: 3, text: this.$t('数量逐层阶梯') }
              ],
              readonly: true,
              disabled: true
            })
          })
          item.queryType = 'select'
          item.valueConverter = {
            type: 'map',
            map: {
              0: this.$t('数量累计阶梯'),
              3: this.$t('数量逐层阶梯')
            }
          }
        } else if (item.field == 'itemExtMap.drawingUrl') {
          item.template = function () {
            return {
              template: cellDrawingView
            }
          }
          item.editTemplate = () => {
            return {
              template: cellDrawingView
            }
          }
          delete item.edit
          item.allowFiltering = false
        } else if (item.field == 'itemExtMap.deliveryPlace') {
          delete item.editConfig
          delete item.formatter
          delete item.edit
        } else if (item.field === 'itemExtMap.requireQuantity') {
          item.allowFiltering = false
          if (item.editConfig) item.editConfig.readonly = true
        } else if (item.field === 'quoteEffectiveNatureDay') {
          // 有效自然日
          item.allowEditing = true
          item.edit = editInstance.create({
            getEditConfig: () => ({
              type: 'number',
              'show-clear-button': true
            })
          })
        } else if (item.field === 'submitPriceTime') {
          // 报价提交时间
          item.allowEditing = false
          item.editConfig.readonly = true
          item.editConfig.disabled = true
        }
        // 合格供应商(如果是成本因子，该逻辑不生效)禁用币种、税率、报价属性、报价生效方式字段
        if (
          this.quotedPriceData.sourcingObjType !== 'cost_factor' &&
          this.quotedPriceData.supplierRange === 'category_qualified' &&
          _fixField.includes(item.field)
        ) {
          item.editConfig.readonly = true
          item.editConfig.disabled = true
          item.allowFiltering = false
        }
      })
      if (this.quotedPriceData.buType === 'GF') {
        _columnData.forEach((item) => {
          if (item.field == 'biddingItemDTO.untaxedUnitPrice') {
            // 光伏，可填写单价(未税)
            item.allowEditing = false
            item.editConfig.readonly = true
            item.editConfig.precision = '5'
          } else if (item.field == 'biddingItemDTO.taxedUnitPrice') {
            item.allowEditing = true
            item.editConfig.readonly = false
            item.editConfig.precision = '5'
          } else if (item.field == 'biddingItemDTO.bidTaxRateName') {
            item.allowEditing = true
            item.editConfig.readonly = false
            item.editConfig.disabled = false
          } else if (item.field == 'biddingItemDTO.bidTaxRateCode') {
            item.allowEditing = true
            item.editConfig.readonly = false
            item.editConfig.disabled = false
          }
        })
      }
      _columnData.unshift({
        field: 'addId',
        width: 0,
        isPrimaryKey: true
      })
      this.$set(this.pageConfig[0].grid, 'columnData', _columnData)
      if (!Array.isArray(this.childFields) || this.childFields?.length <= 0) return // 子级列字段
      let _childColumnData = this.defineGridColumns(utils.cloneDeep(this.childFields))
      // ckd 未税单价、含税单价强控（子级根据未税算含税）
      if (this.quotedPriceData.sourcingObjType === 'combination') {
        _childColumnData.forEach((item) => {
          if (item.field == 'biddingItemDTO.untaxedUnitPrice') {
            // ckd询报价，可填写单价(未税)
            item.allowEditing = true
            item.editConfig.readonly = false
            item.editConfig.precision = '5'
          } else if (item.field == 'biddingItemDTO.taxedUnitPrice') {
            item.allowEditing = false
            item.editConfig.readonly = true
            if (this.quotedPriceData.buType === 'GF') {
              item.editConfig.precision = '5'
            }
          }
        })
      }
      let _w = setChildWidth(_childColumnData, 200, _columnData)
      this.$set(this.pageConfig[0].grid, 'detailTemplate', function () {
        return {
          template: Vue.component('detailTemplate', {
            template: `<div style="width:${_w};">
                          <mt-template-page
                            class="childTemplate"
                            v-clickoutside="clickoutside"
                            ref="childRef"
                            :template-config="childTable"
                          ></mt-template-page>
                        </div>`,
            directives: { clickoutside: clickoutside },
            data: function () {
              return {
                data: {},
                childTable: [
                  {
                    grid: {
                      height: 'auto',
                      lineIndex: true,
                      allowPaging: false,
                      allowEditing: that.childIsEdit,
                      editSettings: {
                        allowAdding: false,
                        allowEditing: that.childIsEdit,
                        allowDeleting: false,
                        mode: 'Normal', // 默认normal模式
                        allowEditOnDblClick: true,
                        showConfirmDialog: false,
                        showDeleteConfirmDialog: false
                      },
                      dataSource: [],
                      columnData: _childColumnData,
                      actionComplete: this.subActionComplete,
                      recordDoubleClick: that.handleParentEndEdit,
                      queryCellInfo: that.customiseCell,
                      class: 'pe-edit-grid custom-toolbar-grid'
                    }
                  }
                ]
              }
            },
            mounted() {
              let data = this.data
              console.log('detail-table', data)
              setTimeout(() => {
                this.childTable[0].grid.dataSource = data?.childItems
              }, 0)
              this.$bus.$on('subGridEndEdit', () => {
                let _childRef = this?.$refs?.childRef
                let _current = _childRef?.getCurrentTabRef()
                if (_current?.grid) {
                  _current?.grid.endEdit()
                }
              })
            },
            methods: {
              clickoutside() {
                this.$refs.childRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
              },
              subActionComplete(e) {
                that.actionComplete(e, 'subItems')
              }
            }
          })
        }
      })
    },
    setSharePriceFields(columns) {
      let hasBidTaxRateCode = columns.find((item) => item.field === 'biddingItemDTO.bidTaxRateCode')
      if (!hasBidTaxRateCode) return columns

      let _sharePriceArr = [
        {
          field: 'biddingItemDTO.realSharePriceTaxed',
          width: 0
        },
        {
          field: 'biddingItemDTO.realSharePriceUntaxed',
          width: 0
        },
        {
          field: 'biddingItemDTO.planSharePriceTaxed',
          width: 0
        },
        {
          field: 'biddingItemDTO.planSharePriceUntaxed',
          width: 0
        },
        {
          field: 'biddingItemDTO.sharePriceUntaxed',
          width: 0
        }
      ]
      _sharePriceArr = _sharePriceArr.filter(
        (item) => !columns.some((obj) => obj.field === item.field)
      )
      return [...columns, ..._sharePriceArr]
    },
    getCurrentTotal() {
      let _diff = 0
      let _cloneObj = this.serializeSaveParams(utils.cloneDeep(this.actionObj))
      let _oldList = this.serializeSaveParams(utils.cloneDeep(this.oldList))
      if (
        Array.isArray(_cloneObj) &&
        _cloneObj.length &&
        Array.isArray(_oldList) &&
        _oldList.length
      ) {
        _cloneObj.forEach((rowData) => {
          let _find = _oldList.find((e) => e.id === rowData.id)
          if (_find) {
            let _currentQuantity = rowData?.itemExtMap?.requireQuantity || 1
            let _currentPrice = this.useTaxedPrice
              ? rowData?.biddingItemDTO?.taxedUnitPrice
              : rowData?.biddingItemDTO?.untaxedUnitPrice || 0
            let _oldQuantity = _find?.itemExtMap?.requireQuantity || 1
            let _oldPrice = this.useTaxedPrice
              ? _find?.biddingItemDTO?.taxedUnitPrice
              : _find?.biddingItemDTO?.untaxedUnitPrice || 0
            if (_currentQuantity !== _oldQuantity || _currentPrice !== _oldPrice) {
              //获取单条数据的差额
              _diff = _diff + (_oldQuantity * _oldPrice - _currentQuantity * _currentPrice)
            }
          }
        })
      }
      const _lastTotal = this.useTaxedPrice ? this.lastTaxTotal : this.lastTotal
      this.currentTotal = (_lastTotal - _diff).toFixed(
        this.ktFlag == 1 || this.isFc || this.isGf ? 5 : PRICE_FRACTION_DIGITS
      )
    },
    async getLastTotal() {
      let _params = {
        rfxId: this.$route.query.rfxId,
        tabType: this.moduleType
      }
      await this.$API.supplyQdetail
        .getRfxBidAmount(_params)
        .then((res) => {
          if (res.code == 200) {
            this.useTaxedPrice = res?.data?.useTaxedPrice
            this.lastTotal = res?.data?.lastTotalAmount || 0
            this.lastTotal = this.lastTotal.toFixed(
              this.ktFlag == 1 || this.isFc || this.isGf ? 5 : PRICE_FRACTION_DIGITS
            )
            this.lastTaxTotal = res?.data?.lastTaxTotalAmount || 0
            this.lastTaxTotal = this.lastTotal.toFixed(
              this.modalData.ktFlag == 1 || this.modalData.fcFlag ? 5 : PRICE_FRACTION_DIGITS
            )
            this.currentTotal = this.useTaxedPrice ? this.lastTaxTotal : this.lastTotal
          }
        })
        .catch(() => {
          this.lastTotal = 0
          this.lastTotal = this.lastTotal.toFixed(
            this.ktFlag == 1 || this.isFc || this.isGf ? 5 : PRICE_FRACTION_DIGITS
          )
          this.lastTaxTotal = 0
          this.lastTaxTotal = this.lastTaxTotal.toFixed(
            this.ktFlag == 1 || this.isFc || this.isGf ? 5 : PRICE_FRACTION_DIGITS
          )
          this.currentTotal = this.useTaxedPrice ? this.lastTaxTotal : this.lastTotal
        })
    },
    isSelectPriceControl() {
      let isSelect = false
      let _priceControl = this.strategyConfig[0]['priceControl']
      if (_priceControl) {
        //如果设置了报价规则，才执行。未设置报价规则，跳过
        if (this.moduleType === 2) {
          //议价Tab，必须执行勾选数据
          isSelect = true
        } else if (this.moduleType === 0) {
          //无限制   首次整单报价(非首次，使用勾选数据)
          if (
            _priceControl === 'unlimited' ||
            (_priceControl === 'first_all' && this.lastTotal !== 0)
          ) {
            isSelect = true
          }
        }
      }
      return isSelect
    },
    getRate(e) {
      const bidTaxRateCode = e.biddingItemDTO.bidTaxRateCode
      const bidTaxRateValue = e.biddingItemDTO.bidTaxRateValue || '0'
      if (bidTaxRateCode) {
        // 通过税率编码获取
        let dataSource = this.taxList
        let fields = { value: 'taxItemCode', text: '__text' }
        if (Array.isArray(dataSource) && typeof fields === 'object') {
          const row = dataSource.find((e) => e[fields?.value || 'value'] === bidTaxRateCode)
          return row ? row?.taxRate : 0
        }
      }
      return bidTaxRateValue
    },
    defineTaxedUnitPrice(data) {
      if (Array.isArray(data) && data.length) {
        data.forEach((e) => {
          // 单价（未税）
          let untaxedUnitPrice = e.biddingItemDTO.untaxedUnitPrice || '0'
          // 单价（含税）
          let taxedUnitPrice = e.biddingItemDTO.taxedUnitPrice || '0'
          // 税率值
          const bidTaxRateValue = this.getRate(e)
          const rate = new Decimal32(bidTaxRateValue).add(1).toString()
          if (bidTaxRateValue >= 0) {
            taxedUnitPrice = new Decimal32(untaxedUnitPrice)
              .mul(rate)
              .toFixed(this.ktFlag == 1 || this.isFc || this.isGf ? 5 : PRICE_FRACTION_DIGITS)
            e.biddingItemDTO.taxedUnitPrice = taxedUnitPrice
          } else {
            e.biddingItemDTO.taxedUnitPrice = 0
          }
        })
      }
    },
    caculateNntaxedUnitPrice(taxedUnitPrice, rate) {
      return new Decimal32(taxedUnitPrice)
        .div(rate)
        .toFixed(this.ktFlag == 1 || this.isFc || this.isGf ? 5 : PRICE_FRACTION_DIGITS)
    },
    async syncOtherLine(data) {
      if (this.isLoading) return
      const { rowData, field } = data
      const _dataSource = utils.cloneDeep(this.actionObj)
      const _fileterDataSource = _dataSource.filter((item) => item.addId !== rowData.addId)
      const _fixCurrency = ['biddingItemDTO.bidCurrencyCode', 'biddingItemDTO.bidCurrencyName']
      const _fixQuote = ['biddingItemDTO.quoteAttribute', 'biddingItemDTO.quoteMode']
      const _taxQuote = [
        'biddingItemDTO.bidTaxRateCode',
        'biddingItemDTO.bidTaxRateName',
        'biddingItemDTO.bidTaxRateValue'
      ]
      const flag =
        (_fixCurrency.includes(field) &&
          _fileterDataSource.some(
            (item) =>
              item.biddingItemDTO['bidCurrencyCode'] || item.biddingItemDTO['bidCurrencyName']
          )) ||
        (_fixQuote.includes(field) &&
          _fileterDataSource.some(
            (item) => item.biddingItemDTO['quoteAttribute'] || item.biddingItemDTO['quoteMode']
          )) ||
        (_taxQuote.includes(field) &&
          _fileterDataSource.some(
            (item) =>
              item.biddingItemDTO['bidTaxRateCode'] ||
              item.biddingItemDTO['bidTaxRateName'] ||
              item.biddingItemDTO['bidTaxRateValue']
          ))
      // 如果其它行存在数据，则不能够同步
      if (flag) return
      const {
        bidCurrencyCode,
        bidCurrencyName,
        quoteAttribute,
        quoteMode,
        bidTaxRateCode,
        bidTaxRateName,
        bidTaxRateValue
      } = rowData.biddingItemDTO
      _dataSource.forEach((item) => {
        item.biddingItemDTO = {
          ...item.biddingItemDTO,
          bidCurrencyCode,
          bidCurrencyName,
          quoteAttribute,
          quoteMode,
          bidTaxRateCode,
          bidTaxRateName,
          bidTaxRateValue
        }
      })
      let params = this.mergeSaveParams(_dataSource)
      // 同步时保存数据
      this.isLoading = true
      await this.$API.supplyQdetail.priceSave(params).catch((err) => {
        console.log(err)
      })
      this.isLoading = false
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>
<style lang="scss">
.childTemplate .e-headercontent thead {
  height: 40px;
}
.childTemplate .grid-container .mt-data-grid .e-grid .e-content {
  min-height: 60px !important;
}
</style>
<style lang="scss" scoped>
.normal-class {
  font: #000;
}
/deep/.bg-grey {
  background: #dedede;
}
/deep/.bg-orange {
  background: #fdf5ea;
}

/deep/ .e-rowcell input[readonly] {
  background: #f5f5f5;
  border-bottom: none;
}
.full-height {
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
  }
  .tabs-wrap {
    position: relative;
    .bid-tips {
      position: absolute;
      // left: 230px;
      z-index: 1;
      top: 15px;
      color: #4d5b6f;
    }
  }
  .hasAggregate /deep/.toolbar-container {
    position: relative;
    top: -5px;
  }
  .hasAggregate /deep/.grid-container {
    position: relative;
    top: 15px;
  }
  .template-wrap {
    position: relative;
    .aggregate-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      z-index: 2;
      top: 40px;
      left: 0;
      background: #fafafa;
      width: 100%;
      padding: 5px 20px;
      border: 1px solid #e8e8e8;

      span {
        color: #9a9a9a;
      }
      .amount {
        display: flex;
        color: #9a9a9a;
        span {
          color: #00469c;
        }
        div {
          &:last-of-type {
            margin-left: 40px;
          }
        }
      }
    }
  }
  // /deep/th.e-headercell {
  //   width: 150px !important;
  // }
  // /deep/td.e-rowcell {
  //   width: 150px !important;
  // }
  // /deep/col {
  //   width: 150px !important;
  // }
  /deep/.e-spinner-pane {
    display: none;
  }
  /deep/.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-table {
    border-left: 1px solid #e0e0e0;
    .e-emptyrow {
      td {
        border: 1px solid #e0e0e0;
        border-top: none;
      }
    }
  }
  /deep/ .e-detailindentcell {
    border-right: none;
  }
  /deep/ .e-detailcell {
    .toolbar-container {
      height: 30px;
    }
  }
}
</style>
