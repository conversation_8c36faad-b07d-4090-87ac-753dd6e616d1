<!--采购明细-->
<template>
  <div class="flex-full-height">
    <div class="tabs-wrap">
      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div class="bid-tips" v-if="bidTips" :style="{ left: tabSource.length * 158 + 'px' }">
        <mt-icon name="icon_card_info"></mt-icon>{{ $t(bidTips) }}
      </div>
    </div>
    <!-- 议价、当前报价、历史报价公用 -->
    <CustomAgGrid
      ref="CustomAgGrid"
      v-clickoutside="clickoutside"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :get-row-id="getRowId"
      :context="context"
      @cell-value-changed="cellValueChanged"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @refresh="refresh"
      @search="search"
    >
      <div slot="header" class="aggregate-container" v-if="moduleType == 0 || moduleType == 2">
        <span>{{ $t('汇总') }}</span>
        <div class="amount">
          <div>
            {{ $t('上次总金额（未税）：') }}<span>{{ lastTotal }}</span>
          </div>
          <div>
            {{ $t('本次总金额（未税）：') }}<span>{{ currentTotal }}</span>
          </div>
        </div>
      </div>
    </CustomAgGrid>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import clickoutside from '@/directive/clickoutside'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellLink from '@/components/AgCellComponents/cellLink'
import { columnData as rfxItem } from '@/views/common/columnData/ag/item'
import { columnData as biddingItem } from '@/views/common/columnData/ag/biddingItem'
import { columnData as itemExt } from '@/views/common/columnData/ag/itemExt'
import cloneDeep from 'lodash/cloneDeep'
import { v4 as uuidv4 } from 'uuid'
import { utils } from '@mtech-common/utils'
import { Decimal32 } from '../../../../utils/utils'
import Decimal from 'decimal.js'
import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'
import { stepNotMergeField } from '@/views/common/columnData/constant' //阶梯不合并字段
import {
  fieldDefinesMap,
  setStepField,
  addRowSpan,
  rowSpan,
  cellClass,
  cellClassNoBg
} from '@/views/common/columnData/utils'

import {
  inputCurrency,
  inputBidPurUnit,
  inputBidTaxRate,
  inputPrice,
  inputQuote,
  inputQuantity,
  inputEffectiveDate,
  judgePurAndQuantiry,
  judgeUnitExist,
  judgeUnitConsistent
} from '@/views/supply/quotationBidding/detail/utils/ag/aggridInputHandlers'

export default {
  directives: { clickoutside: clickoutside },
  inject: ['reload'],
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    fieldDefines: {
      type: Array,
      default: () => {
        return []
      }
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    //可编辑字段
    submitField: {
      type: Array,
      default: () => {
        return []
      }
    },
    parentRequired: {
      type: Array,
      default: () => {
        return []
      }
    },
    childRequired: {
      type: Array,
      default: () => {
        return []
      }
    },
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 币种编码
    currencyList: {
      type: Array,
      default: () => []
    },
    // 税率
    taxList: {
      type: Array,
      default: () => []
    },
    // 采购单位
    purUnitList: {
      type: Array,
      default: () => []
    },
    rfxCountdown: {
      type: Object,
      default: () => ({})
    },
    newPrice: {
      type: Number,
      default: 0
    },
    strategyConfig: {
      type: Array,
      default: () => []
    },
    ktFlag: {
      type: Number,
      default: () => {}
    }
  },
  watch: {
    moduleType: {
      immediate: true,
      handler(v) {
        this.$set(this.downTemplateParams, 'tabType', v)
      }
    },
    $route(to, from) {
      if (from.name === 'offer-purchase-cost') {
        // 由成本分析页面返回，则刷新当前页面
        this.refresh()
      }
    }
  },
  components: {
    CustomAgGrid,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    // eslint-disable-next-line
    cellFile, // 单元格文件查看
    // eslint-disable-next-line
    cellLink // 单元格点击跳转
  },
  data() {
    return {
      agGrid: null,
      tabSource: [],
      moduleType: this.quotedPriceData.transferStatus != 32 ? 0 : 2,
      searchConfig: [],
      toolbar: [],
      tableData: [],
      columns: [],
      rowSelections: null,
      isStopEditingOnBlur: false,
      //维护数组
      actionObj: [],
      sourceInfo: [],
      oldList: [], //判断是否保存旧数据
      requestUrls: {},
      downTemplateName: this.$t('采购明细模板'),
      downTemplateParams: {
        pageFlag: false,
        rfxId: this.$route.query.rfxId,
        tabType: 0
      },
      lastTotal: 0,
      currentTotal: 0,
      txCompanyList: ['2M01', '2S06']
    }
  },
  computed: {
    // 是否可以报价
    canQuote() {
      return [31, 32, 41].includes(Number(this.quotedPriceData.transferStatus)) // 报价中 议价中 商务投标中
    },
    abateFlag() {
      // 议价标识
      return this.moduleType == 2 ? true : false
    },
    bidTips() {
      if (!Array.isArray(this.strategyConfig) || !this.strategyConfig.length) {
        return ''
      }
      let _priceControl = this.strategyConfig[0]['priceControl']
      const STR1 = this.$t('说明：本次寻源不限制报价行数，请勾选您需要提交报价的行')
      const STR2 = this.$t('说明：本次是整单报价，您需要对所有进行报价')
      const STR3 = this.$t('说明：本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行')
      const STR4 = this.$t('说明：请勾选您需要提交报价的行')
      let _tip = ''
      if (this.moduleType === 0) {
        //当前报价
        switch (_priceControl) {
          case 'unlimited':
            _tip = STR1
            break
          case 'all':
            _tip = STR2
            break
          case 'first_all':
            //firstPrice  1 首次 0 非首次
            if (this.quotedPriceData.firstPrice == 1) {
              _tip = STR2
            } else {
              _tip = STR3
            }
            break
          default:
            _tip = ''
            break
        }
      } else if (this.moduleType === 2) {
        _tip = STR4
      } else {
        _tip = ''
      }
      return _tip
    },
    transferStatus() {
      return this.quotedPriceData?.transferStatus //流程状态
    },
    status() {
      return this.quotedPriceData?.status // xx
    },
    joinStatus() {
      return this.quotedPriceData?.joinStatus // 是否参与
    }
  },
  beforeDestroy() {
    this.$bus.$off('procurementRefreshPage')
  },
  beforeMount() {
    this.context = { componentParent: this }
  },
  async mounted() {
    await this.getTxCompanyList()
    this.init()
    // 点击topInfo按钮后，更新表格
    this.$bus.$on(`procurementRefreshPage`, () => {
      this.reload() // 参与后直接刷页面
    })
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.dictionaryGetList(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<< 事件操作 >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // tab - 切换页签
    handleSelectTab(idx) {
      this.moduleType = this.tabSource[idx].moduleType
      // 初始数据
      this.initSearchConfig()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.addId
    },
    // ag - toolbar点击监听
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Save': // 保存
          this.handleSave()
          break
        case 'ReQuote': // 重新报价
          this.handleReQuote()
          break
        case 'Import': // 导入
          this.handleImport()
          break
        default:
          break
      }
    },
    // ag - 监听select选择框
    onRowSelected(e) {
      if (!e.data.isFirstLine) return
      let isSelected = e.node.selected
      let itemGroupId = e.data.itemGroupId
      //如果勾选的是阶梯第一行数据,同步其他行
      this.agGrid.api.forEachNode((node) => {
        if (node.data.itemGroupId === itemGroupId && !node.data.isFirstLine) {
          node.setSelected(isSelected)
        }
      })
    },
    // ag - 编辑框监听
    cellValueChanged(params) {
      if (params.oldValue === params.newValue) return
      inputCurrency(params) //币种联动
      inputBidPurUnit(params, this.agGrid, this.columns) //订单单位联动
      inputQuote(params) //报价方式、报价属性联动
      inputBidTaxRate(params, this.agGrid, this.columns) //税率联动 params:当前单元格参数，agGrid:aggrid对象，columns:列配置
      inputQuantity(params) //最小采购量、最小包装量联动
      inputEffectiveDate(params, this.quotedPriceData) //有效期联动监听(1503公司)
      inputPrice(params, this.agGrid, this.columns) //价格计算
      this.syncOtherStepLine(params) //同步阶梯行
      this.getCurrentTotal(params) //本次总金额
    },
    // 编辑框监听 - 阶梯同步其他行数据
    syncOtherStepLine(params) {
      const field = params.column.colId
      const itemGroupId = params.data.itemGroupId
      const isFirstLine = params.data.isFirstLine
      if (!isFirstLine || stepNotMergeField.includes(field) || !itemGroupId) return
      // 查询同组阶梯数据
      this.agGrid.api.forEachNode((node) => {
        if (!node.data.isFirstLine && node.data.itemGroupId === itemGroupId) {
          node.setDataValue(field, params.value)
        }
      })
    },
    // ag - 点击事件监听
    clickoutside() {
      this.agGrid.api.stopEditing()
    },
    // btn - 保存
    async handleSave(isAutoSave) {
      this.agGrid.api.stopEditing()
      // 保存后待数据同步执行完后再执行(延时1s)
      await this.delay(1000)
      // 全量数据保存
      let _dataSource = cloneDeep(this.tableData)
      this.saveComFn(_dataSource, 'save', isAutoSave)
    },
    // btn - 提交
    handleSubmit() {
      // 所有数据
      const _allDataSource = cloneDeep(this.tableData)
      // 选中的数据
      const _selctDataSource = this.agGrid.api.getSelectedRows()
      // 提交数据 议价、报价-无限制、报价-首次整单报价（首次）、勾选数据，其他情况下全量（此处采购明细需要保持在报价|议价页面）
      const _flag =
        this.moduleType === 2 ||
        (this.moduleType === 0 && this.strategyConfig[0]['priceControl'] === 'unlimited') ||
        (this.moduleType === 0 &&
          this.strategyConfig[0]['priceControl'] === 'first_all' &&
          this.quotedPriceData.firstPrice == 0)
      const _dataSource = _flag ? _selctDataSource : _allDataSource
      // 校验提交数据
      let _submitFlag = this.validSubmitData(_dataSource, _flag)
      if (!_submitFlag) return
      this.saveComFn(_dataSource, 'submit')
    },
    // 保存 - 保存、提交公共函数
    saveComFn(data, type, isAutoSave) {
      // 校验数据
      let _valid = this.validData(data)
      if (!_valid?.flag) return
      // 数据组合
      let _data = this.combineData(data)
      // 保存参数组合
      let params = {
        abateFlag: this.moduleType == 2 ? true : false, // 议价标识
        bidPriceItems: _data,
        rfxId: this.quotedPriceData.rfxId,
        submitStatus: type === 'submit' ? 1 : 0,
        tenantId: this.quotedPriceData.tenantId,
        newPrice: this.moduleType == 2 ? this.newPrice : null // 议价时提交
      }
      // 判断单位是否一致(不一致弹框提示)
      if (!_valid.isUnitConsistent) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('采购单位和基本单位不一致，请确认转换率是否正确')
          },
          success: () => {
            // 提示确认后后保存提交
            this.saveData(params, isAutoSave)
          }
        })
        return
      }
      // 保存提交
      this.saveData(params, isAutoSave)
    },
    // 保存 - 提交数据校验
    validSubmitData(data, isNeedSelect) {
      let _priceControl = this.strategyConfig[0]['priceControl']
      //如果需要勾选数据
      if (isNeedSelect && (!data || !data?.length)) {
        let _msg =
          this.moduleType === 2
            ? this.$t('请勾选您需要提交报价的行')
            : _priceControl === 'unlimited'
            ? this.$t('本次寻源不限制报价行数，请勾选您需要提交报价的行')
            : this.$t('本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行')
        this.$toast({
          content: _msg,
          type: 'warning'
        })
        return false
      }
      if ([31, 32, 41].includes(this.transferStatus)) {
        let { checked, checkedMsg } = this.validRequiredFields(data)
        if (!checked) {
          this.$toast({
            content: `采购明细Tab，${checkedMsg}`,
            type: 'error'
          })
          return false
        }
      }
      return true
    },
    // 保存 - 必填字段校验（暂不校验子集）
    validRequiredFields(data) {
      let _parentRequired = utils.cloneDeep(this.parentRequired)
      let checked = true,
        checkedMsg = ''
      for (let i = 0; i < data.length; i++) {
        let p = data[i]
        let { res, msg } = this.checkFields(p, _parentRequired)
        if (!res) {
          checked = false
          checkedMsg = msg
          break
        }
      }
      return { checked, checkedMsg }
    },
    // 保存 - 校验数据
    checkFields(obj, fields) {
      let res = true,
        msg = ''
      for (let i = 0; i < fields.length; i++) {
        let field = fields[i]['fieldCode']
        if (!obj['biddingItemDTO'][field] && obj['biddingItemDTO'][field] !== 0) {
          res = false
          let _field = this.fieldDefines.find((e) => e.field === `biddingItemDTO.${field}`)
          if (_field?.headerText) {
            msg = `字段：${_field.headerText}，为必填项`
          }
          break
        }
      }
      return { res, msg }
    },
    // 保存 - 数据校验
    validData(data) {
      let flag = true
      let isUnitConsistent = true
      if (!data || !data?.length) return false
      data.forEach((item) => {
        let minPurQuantity = item.biddingItemDTO.minPurQuantity
        let minPackageQuantity = item.biddingItemDTO.minPackageQuantity
        // 校验最小采购量&最小包装量
        if (
          judgePurAndQuantiry(minPurQuantity, minPackageQuantity) &&
          !this.txCompanyList.includes(this.$route.query?.companyCode)
        ) {
          this.$toast({
            content: this.$t('最小采购量需是最小包装数量的整数倍'),
            type: 'warning'
          })
          flag = false
          return
        }
        // 校验采购单位与基本单位是否一致
        if (judgeUnitExist(this.columns) && !judgeUnitConsistent(item)) {
          isUnitConsistent = false
          return
        }
      })
      return { flag, isUnitConsistent }
    },
    // 保存 - 数据组装
    combineData(data) {
      return data.map((item) => {
        let _obj = {}
        // submitField字段处理
        this.submitField.forEach((field) => {
          let _fieldCode = field.fieldCode
          if (_fieldCode === 'supplierDrawing') {
            //如果供应商附件 处理附件id 为sysFileId
            _obj['supplierFileList'] = this.processDrawingData(item[_fieldCode])
            return
          }

          _obj[_fieldCode] =
            _fieldCode in item['biddingItemDTO']
              ? item['biddingItemDTO'][_fieldCode]
              : item[_fieldCode]
          // 优先取报价行 || 明细行
        })
        //其他字段处理
        let _otherFields = {
          biddingId: item['biddingItemDTO']?.biddingId || null,
          biddingItemId: item['biddingItemDTO']?.biddingItemId || null,
          rfxItemId: item?.rfxItemId || null,
          priceUnitName: item?.priceUnitName,
          itemStages: item?.itemStages,
          biddingItemLogisticsDTO: item['biddingItemLogisticsDTO'] //物流参数
        }
        _obj = Object.assign({}, _obj, _otherFields)
        return _obj
        // *特殊处理,如果未配置bidTaxRateValue，根据bidTaxRateCode来取（建议策略配置该字段）,暂不作处理，跟进
      })
    },
    // 保存 -数据组装 - 处理附件数据
    processDrawingData(files) {
      if (!files) return []
      let _files = JSON.parse(files)
      _files.forEach((file) => {
        if (file?.sysFileId) return file
        file.sysFileId = file.id
        delete file.id
      })
      return _files
    },
    // 保存 - 接口提交
    saveData(params, isAutoSave) {
      this.$API.supplyQdetail.priceSave(params).then((res) => {
        if (res.code == 200) {
          if (!isAutoSave) {
            // 自动保存不用提示
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            //重刷表格
            this.initTableData()
            this.$emit('mainparticipateButton')
          }
        }
      })
    },
    // 保存 - 序列化数据 (暂不处理父子结构)
    serializeSaveParams(list) {
      let res = []
      list.forEach((e) => {
        let _sub = e?.childItems
        delete e.childItems
        res.push(e)
        if (Array.isArray(_sub)) {
          res = res.concat(_sub)
        }
      })
      return res
    },
    // btn - 重新报价
    handleReQuote() {
      this.$emit('update:newPrice', 1)
      // this.fieldDefines.forEach((item) => { //重新报价编辑逻辑控制todo
      //   // //可以编辑
      //   const allowEditingTbs = ['mt_supplier_bidding_item', 'mt_supplier_bidding_item_logistics']
      //   if (allowEditingTbs.includes(item.tableName)) {
      //     item.allowEditing = true
      //   } else {
      //     item.allowEditing = false
      //   }
      // })
      if (this.moduleType == 0) {
        this.tableData = this.resetQuoteParams(this.tableData)
      }
      this.$toast({
        content: this.$t('请调整物料价格，提交报价！'),
        type: 'warning'
      })
    },
    // 重新报价 - 重置参数
    resetQuoteParams(list) {
      if (!Array.isArray(list)) return []
      list.forEach((e) => {
        e['biddingItemDTO'].biddingId = 0
        e['biddingItemDTO'].biddingItemId = 0
        e['biddingItemDTO'].submitStatus = 0
      })
      return list
    },
    // btn - 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'rfxRequireDetail',
        templateUrl: 'supExportPriceTemplete',
        uploadUrl: `supImportPriceTemplete`,
        rfxId: this.$route.query.rfxId
      }
      this.showUploadExcel(true)
    },
    // 导入 - 弹框显示
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入 - 上传确认
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    // btn - 查询
    search(params) {
      this.initTableData(params)
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 初始化 - 页面
    init() {
      this.initTab()
      this.initSearchConfig()
      this.initTotalInfo()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // 初始化 - tab页签
    initTab() {
      let _tabSource = [
        { title: this.$t('议价'), moduleType: 2 },
        { title: this.$t('当前报价'), moduleType: 0 },
        { title: this.$t('历史报价'), moduleType: 1 }
      ]
      if (this.quotedPriceData.transferStatus != 32) {
        //非议价状态没有议价tab页
        _tabSource.shift()
      }
      this.tabSource = _tabSource
    },
    // 初始化 - 查询区域
    initSearchConfig() {
      let _config = [
        {
          field: 'itemCode',
          label: this.$t('物料编码')
        },
        {
          field: 'rfx_item.itemName',
          label: this.$t('物料名称')
        },
        {
          field: 'rfx_item.temporaryItemCode',
          label: this.$t('临时物料编码')
        },
        {
          field: 'rfx_item.categoryCode',
          label: this.$t('品类编码')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar
    initToolbar() {
      if (this.moduleType === 1) {
        // 历史报价 不显示
        this.toolbar = []
        return
      }
      let _toolbar = [
        {
          id: 'Save',
          icon: 'icon_solid_Save',
          title: this.$t('保存')
        },
        {
          id: 'ReQuote',
          icon: 'icon_solid_edit',
          title: this.$t('重新报价')
        },
        {
          id: 'Import',
          icon: 'icon_solid_Import',
          title: this.$t('导入')
        }
      ]
      this.toolbar = this.status === 1 && this.joinStatus === 1 ? _toolbar : []
    },
    // 初始化 - 汇总信息（上次总金额，本次总金额）
    initTotalInfo() {
      this.getLastTotal()
    },
    // 汇总信息 - 获取本次总金额(未税总金额之和)
    getCurrentTotal(params) {
      const field = params.column.colId
      if (field !== 'biddingItemDTO.taxedUnitPrice') return
      let _dataSource = cloneDeep(this.tableData)
      let _currentTotal = 0
      _dataSource.forEach((item) => {
        if (item.biddingItemDTO.taxedUnitPrice)
          _currentTotal = _currentTotal + Number(item.biddingItemDTO.taxedUnitPrice)
      })
      this.currentTotal = _currentTotal.toFixed(2)
    },
    // 汇总信息 - 获取上次总金额
    async getLastTotal() {
      let _params = {
        rfxId: this.$route.query.rfxId,
        tabType: this.moduleType
      }
      this.$API.supplyQdetail
        .getRfxBidAmount(_params)
        .then((res) => {
          if (res.code == 200) {
            this.lastTotal = res?.data?.lastTotalAmount || 0
            this.lastTotal = this.lastTotal.toFixed(this.ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
            this.currentTotal = this.lastTotal
          }
        })
        .catch(() => {
          this.lastTotal = 0
          this.lastTotal = this.lastTotal.toFixed(this.ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
          this.currentTotal = this.lastTotal
        })
    },
    // 初始化 - 表头
    initGridColumns() {
      // this.fieldDefines 采购明细主页面已作处理，沿用之前逻辑，保留field,headerText,allowEdit,eidtConfig此处阶梯根据this.fieldDefines配置ag相关属性
      let _columnData = this.defineGridColumns(utils.cloneDeep(this.fieldDefines))
      // 阶梯相关字段顺序调整
      _columnData = setStepField(_columnData, stepNotMergeField)
      // 设置表头属性
      _columnData = this.setColumnsAttribute(_columnData)
      this.columns = cloneDeep(_columnData)
    },
    // 表头 - 获取表头是否可编辑
    getGridEditAble() {
      // 未参与
      if (this.quotedPriceData.joinStatus === 0) return false
      // 倒计时内
      if (this.rfxCountdown.countDown <= Date.now()) return false
      // 议价中 & 议价页签可编辑
      if (this.moduleType === 2 && this.quotedPriceData.transferStatus === 32) return true
      // 报价中|| 商务投标中 采购明细可以编辑
      if (this.moduleType === 0 && [31, 41].includes(this.quotedPriceData.transferStatus))
        return true
      return false
    },
    // 表头 - 列字段处理
    defineGridColumns(fieldDefines) {
      let _columnData = [...fieldDefines].map(fieldDefinesMap)
      let _preColumns = [
        {
          field: 'lineNo',
          width: 65,
          headerText: this.$t('行号'),
          allowEditing: false,
          allowFiltering: false
        },
        {
          field: 'biddingItemDTO.priceStatus',
          width: 90,
          headerText: this.$t('状态'),
          allowEditing: false
        }
      ]
      _columnData = _preColumns.concat(_columnData)
      if (this.moduleType === 2) {
        //如果是议价tab,添加’议价理由‘
        _columnData.splice(2, 0, {
          field: 'biddingItemDTO.abateReason',
          headerText: this.$t('议价理由'),
          allowEditing: false
        })
      }
      // 添加勾选框
      if (this.isSelectControl()) {
        _columnData.unshift({ option: 'checkboxSelection', width: 55 })
      }
      return _columnData
    },
    // 表头 - 设置ag表头配置相关属性
    setColumnsAttribute(columns) {
      let _editable = this.getGridEditAble()
      let _base = rfxItem()
      let _biddingItemDTO = biddingItem({
        prefix: 'biddingItemDTO.',
        companyCode: this.quotedPriceData.companyCode,
        ktFlag: this.ktFlag,
        quotedPriceData: this.quotedPriceData,
        currencyList: this.currencyList,
        taxList: this.taxList,
        purUnitList: this.purUnitList
      })
      let _itemExtMap = itemExt({
        prefix: 'itemExtMap.'
      })
      let _all = [..._base, ..._biddingItemDTO, ..._itemExtMap]
      // 此处匹配必填项（应在columnMixin.js中添加required字段）
      let _requiredField = this.parentRequired.map((item) => item.fieldCode)
      let _columns = columns.map((item) => {
        let _fieldArr = item.field?.split('.')
        let _field = _fieldArr?.length > 1 ? _fieldArr[1] : item.field
        let required = _requiredField.includes(_field)
        // checkBox 特殊处理
        if (item.option === 'checkboxSelection') {
          return {
            ...item,
            rowSpan: rowSpan,
            cellClass: _editable ? cellClass : cellClassNoBg
          }
        }
        let _attrObj = {
          required,
          field: item.field || '',
          headerName: item.headerText,
          editable: _editable && item.allowEditing,
          editConfig: item.editConfig,
          width: item.width,
          rowSpan: rowSpan
        }
        // 特殊处理 - 成本分析
        if (['costAnalysis'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellLink'
          _attrObj.cellRendererParams = {
            isAutoSave: true,
            handleable: _editable, //“可操作”（不可编辑），取editable避免从历史报价进入成本分析页面
            rfxId: this.$route.query.rfxId,
            abateFlag: this.quotedPriceData.transferStatus === 32 // 还价标志：议价为true，否则为false
          }
        }
        // 特殊处理 - 附件查看
        if (['drawing', 'supplierDrawing'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellFile'
          _attrObj.cellRendererParams = {
            handleable: _editable && item.field === 'supplierDrawing' //供应商附件“可操作”（不可编辑）
          }
        }
        // 特殊处理 - 合格供应商禁用币种、税率、报价属性、报价生效方式字段
        if (
          this.quotedPriceData.supplierRange === 'category_qualified' &&
          [
            'biddingItemDTO.bidCurrencyCode',
            'biddingItemDTO.bidCurrencyName',
            'biddingItemDTO.quoteAttribute',
            'biddingItemDTO.quoteMode',
            'biddingItemDTO.bidTaxRateCode',
            'biddingItemDTO.bidTaxRateName',
            'biddingItemDTO.bidTaxRateValue'
          ].includes(item.field)
        ) {
          _attrObj.editable = false
        }
        const _find = _all.find((x) => x.field === item.field)
        if (_find) {
          // 特殊处理 (非编辑页签editable控制)
          if ('editable' in _find) {
            _find.editable = _editable && _find.editable
          }

          _attrObj.editConfig = _find?.editConfig
            ? { ..._attrObj.editConfig, ..._find?.editConfig }
            : { ..._attrObj.editConfig }
          delete _find.editConfig
          _attrObj = Object.assign({}, _attrObj, _find)
        }
        // 1.如果表格可以编辑=>根据颜色来区分是否可编辑
        // 2.如果表格不可编辑=>表格不用根据颜色来区分是否编辑状态
        _attrObj.cellClass = _editable ? cellClass : cellClassNoBg
        return _attrObj
      })
      return _columns
    },
    // 表头 - 工具 - 判断是否需要选择框
    isSelectControl() {
      let isSelect = false
      let _priceControl = this.strategyConfig[0]['priceControl']
      if (this.quotedPriceData.joinStatus === 0) return false // 未参与不显示select框
      if (_priceControl) {
        //如果设置了报价规则，才执行。未设置报价规则，跳过
        if (this.moduleType === 2) {
          //议价Tab，必须执行勾选数据
          isSelect = true
        } else if (this.moduleType === 0) {
          //无限制   首次整单报价(非首次，使用勾选数据)
          if (
            _priceControl === 'unlimited' ||
            // (_priceControl === 'first_all' && this.lastTotal !== 0)
            (_priceControl === 'first_all' && this.quotedPriceData.firstPrice === 0)
          ) {
            isSelect = true
          }
        }
      }
      return isSelect
    },
    // 初始化 - 表格数据
    async initTableData(rules) {
      this.$store.commit('startLoading')
      this.tableData = []
      let params = this.mergeParams(rules)
      const res = await this.$API.supplyQdetail.tenderItems(params).catch(() => {})
      if (res) {
        let records = cloneDeep(res.data?.records || [])
        let list = await this.serializeList(records)
        this.tableData = [...list]
        this.$store.commit('endLoading')
      }
    },
    // 表格数据 - 拼接请求参数
    mergeParams(rules) {
      let params = {
        rfxId: this.$route.query.rfxId,
        tabType: this.moduleType,
        queryBuilderDTO: {
          page: {
            current: 1,
            size: 10000
          }
        }
      }
      if (rules) {
        params.queryBuilderDTO = Object.assign(params.queryBuilderDTO, rules)
      }
      return params
    },
    // 表格数据 - 初始处理数据
    async serializeList(list) {
      this.$emit('update:newPrice', 0)
      this.serializeGridList(list)
      // this.setSharePriceUntaxed(list) // 前端初始不参与计算分摊后单价未税
      await this.initSourceInfo(list)
      this.actionObj = list
      this.oldList = utils.cloneDeep(list)
      return list
    },
    // 表格数据 - 序列化数据
    serializeGridList(list) {
      list.forEach((row) => {
        row.addId = uuidv4()
        if (row.biddingItemDTO && typeof row.biddingItemDTO === 'object') {
          // 获取采方税率 ?? 单据税率
          const taxRateCode = row.taxRateCode || this.quotedPriceData?.taxRateCode
          // 获取采方币种 ?? 单据币种
          const currencyCode =
            row?.biddingItemDTO?.currencyCode || this.quotedPriceData?.currencyCode

          if (
            taxRateCode &&
            !row.biddingItemDTO.bidTaxRateCode &&
            !row.biddingItemDTO.bidTaxRateValue
          ) {
            const taxRow = this.taxList.find((e) => e.taxItemCode === taxRateCode)
            if (taxRow) {
              row.biddingItemDTO.bidTaxRateCode = taxRow.taxItemCode
              row.biddingItemDTO.bidTaxRateName = taxRow.taxItemName
              row.biddingItemDTO.bidTaxRateValue = taxRow.taxRate
            }
          }

          if (row.biddingItemDTO.bidTaxRateCode && !row.biddingItemDTO.bidTaxRateValue) {
            const taxRow = this.taxList.find(
              (e) => e.taxItemCode === row.biddingItemDTO.bidTaxRateCode
            )
            if (taxRow) {
              row.biddingItemDTO.bidTaxRateCode = taxRow.taxItemCode
              row.biddingItemDTO.bidTaxRateName = taxRow.taxItemName
              row.biddingItemDTO.bidTaxRateValue = taxRow.taxRate
            }
          }

          if (
            currencyCode &&
            (!row.biddingItemDTO.bidCurrencyCode || !row.biddingItemDTO.bidCurrencyName)
          ) {
            const findRow = this.currencyList.find((e) => e.currencyCode === currencyCode)
            if (findRow) {
              row.biddingItemDTO.bidCurrencyCode = findRow.currencyCode
              row.biddingItemDTO.bidCurrencyName = findRow.currencyName
            }
          }

          if (!row.biddingItemDTO.bidPurUnitCode) {
            row.biddingItemDTO.bidPurUnitName = ''
          }

          // 获取采方采购单位
          if (
            row.purUnitCode &&
            (!row.biddingItemDTO.bidPurUnitCode || !row.biddingItemDTO.bidPurUnitName)
          ) {
            const findRow = this.purUnitList.find((e) => e.unitCode === row.purUnitCode)
            if (findRow) {
              row.biddingItemDTO.bidPurUnitCode = findRow.unitCode
              row.biddingItemDTO.bidPurUnitName = findRow.unitName
            }
          }

          // 转换率
          if (!row.biddingItemDTO.bidConversionRate && row.conversionRate) {
            row.biddingItemDTO.bidConversionRate = row.conversionRate
          }

          // 整机模组外发，单价未税=运费+加工费+加工费材料费
          if (this.quotedPriceData.sourcingObjType === 'module_out_going') {
            const { transportCost, processCost, processPartyMaterialCost, bidTaxRateValue } =
              row.biddingItemDTO

            // 单价（未税）
            row.biddingItemDTO.untaxedUnitPrice =
              transportCost + processCost + processPartyMaterialCost || 0

            // 单价（含税）
            let taxedUnitPrice = 0
            const rate = new Decimal32(bidTaxRateValue || 0).add(1).toString()
            if (bidTaxRateValue >= 0) {
              taxedUnitPrice = new Decimal32(row.biddingItemDTO.untaxedUnitPrice)
                .mul(rate)
                .toFixed(this.ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
              row.biddingItemDTO.taxedUnitPrice = taxedUnitPrice
            }
            row.biddingItemDTO.taxedUnitPrice = taxedUnitPrice
          }
        }
        row.stepNum = row.stepValue
        row.drawing = row.drawings ? JSON.stringify(row.drawings) : null //单独处理附件字段
        row.stepQuoteName =
          row.itemStageList && row.itemStageList.length > 0
            ? JSON.stringify(row.itemStageList)
            : null //单独处理阶梯报价
        row.supplierDrawing = row.supplierFileList ? JSON.stringify(row.supplierFileList) : null //单独处理"供应商附件"字段
        if (row.itemExtMap && row.itemExtMap.minQuoteRangeType == 1) {
          row.itemExtMap.minQuoteRange = new Decimal(row.itemExtMap.minQuoteRange)
            .mul(new Decimal(100))
            .toNumber()
        }
        let _subItems = row['childItems'] // 阶梯暂无childItems
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }
      })
      list = addRowSpan(list)
    },
    // 表格数据 - 初始化处理 供应商信息, 关联报价属性、报价方式
    async initSourceInfo(dataSource) {
      if (!this.$route.query.rfxId || !this.quotedPriceData) {
        return
      }
      const categoryCodeList = dataSource.filter((e) => e.categoryCode).map((e) => e.categoryCode)
      const res = await this.$API.supplierTender
        .getSourceInfo({
          siteCode: this.quotedPriceData.siteCode,
          categoryCodeList,
          rfxId: this.$route.query.rfxId
        })
        .catch(() => {})
      if (!res || !Array.isArray(res.data)) {
        return
      }
      this.sourceInfo = this.sourceInfoConverter(res.data)
      // 合并报价方式、报价属性
      dataSource.forEach((ds) => {
        if (typeof ds.biddingItemDTO === 'object') {
          const sourceInfoRow = this.sourceInfo.find((e) => e.categoryCode === ds.categoryCode)
          //
          if (sourceInfoRow) {
            // quoteAttribute offerAttribute=报价属性 如果匹配到报价属性, 则不可编辑
            if (!ds.biddingItemDTO.quoteAttribute && sourceInfoRow.offerAttribute) {
              ds.biddingItemDTO.quoteAttribute = sourceInfoRow.offerAttribute
            }
            // quoteMode priceEffectiveMode=报价方式 如果匹配到报价方式，则不可编辑
            if (!ds.biddingItemDTO.quoteMode && sourceInfoRow.priceEffectiveMode) {
              ds.biddingItemDTO.quoteMode = sourceInfoRow.priceEffectiveMode
            }
          }
        }
      })
    },
    // 表格数据 - 转换报价属性 价格生效方式
    sourceInfoConverter(sourceInfo) {
      for (const row of sourceInfo) {
        const offerAttributeMap = {
          1: 'mailing_price',
          2: 'standard_price'
        }
        const priceEffectiveModeMap = {
          5: 'in_warehouse',
          1: 'order_date'
        }
        priceEffectiveModeMap['NULL'] = 'out_warehouse'
        row.offerAttribute = offerAttributeMap[row.offerAttribute]
        row.priceEffectiveMode = priceEffectiveModeMap[row.priceEffectiveMode]
      }
      return sourceInfo
    },
    // 表格数据 - 设置上次分摊价格
    setSharePriceUntaxed(list) {
      // sharePriceUntaxed  分摊后单价（未税）
      // untaxedUnitPrice   单价（未税）
      // realSharePriceUntaxed  实际分摊价（未税
      //分摊价=父不含税单价+（子分摊为是实际分摊价）
      list.forEach((row) => {
        let _sharePriceUntaxed = 0
        _sharePriceUntaxed += +row.biddingItemDTO?.untaxedUnitPrice ?? 0
        let _subItems = row['childItems']
        if (Array.isArray(_subItems)) {
          _subItems.forEach((e) => {
            _sharePriceUntaxed += +e.biddingItemDTO?.realSharePriceUntaxed ?? 0
          })
        }
        row.biddingItemDTO.sharePriceUntaxed = _sharePriceUntaxed
      })
    },
    // 表格数据 - 工具 - 获取税率
    getRate(e) {
      const bidTaxRateCode = e.biddingItemDTO.bidTaxRateCode
      const bidTaxRateValue = e.biddingItemDTO.bidTaxRateValue || '0'
      if (bidTaxRateCode) {
        // 通过税率编码获取
        let dataSource = this.taxList
        let fields = { value: 'taxItemCode', text: '__text' }
        if (Array.isArray(dataSource) && typeof fields === 'object') {
          const row = dataSource.find((e) => e[fields?.value || 'value'] === bidTaxRateCode)
          return row ? row?.taxRate : 0
        }
      }
      return bidTaxRateValue
    },
    // 表格 - 工具 - 延时提交
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.normal-class {
  font: #000;
}
/deep/.bg-grey {
  background: #dedede;
}
/deep/.bg-orange {
  background: #fdf5ea;
}

/deep/ .e-rowcell input[readonly] {
  background: #f5f5f5;
  border-bottom: none;
}
.full-height {
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
  }
  .tabs-wrap {
    position: relative;
    .bid-tips {
      position: absolute;
      // left: 230px;
      z-index: 1;
      top: 15px;
      color: #4d5b6f;
    }
  }
  /deep/.toolbar-container {
    position: relative;
    top: -10px;
  }
  .aggregate-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fafafa;
    width: 100%;
    padding: 5px 20px;
    border: 1px solid #babfc7;
    border-bottom: none;
    span {
      color: #9a9a9a;
    }
    .amount {
      display: flex;
      color: #9a9a9a;
      span {
        color: #00469c;
      }
      div {
        &:last-of-type {
          margin-left: 40px;
        }
      }
    }
  }
  /deep/.e-spinner-pane {
    display: none;
  }
  /deep/.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-table {
    border-left: 1px solid #e0e0e0;
    .e-emptyrow {
      td {
        border: 1px solid #e0e0e0;
        border-top: none;
      }
    }
  }
  /deep/ .e-detailindentcell {
    border-right: none;
  }
  /deep/ .e-detailcell {
    .toolbar-container {
      height: 30px;
    }
  }
}
</style>
